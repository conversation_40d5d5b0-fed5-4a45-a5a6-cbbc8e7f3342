/* eslint-disable */
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`. */
  DateTime: { input: any; output: any; }
  Upload: { input: any; output: any; }
};

export type AppImage = {
  __typename?: 'AppImage';
  hash: Scalars['String']['output'];
  height?: Maybe<Scalars['Int']['output']>;
  id: Scalars['Int']['output'];
  order: Scalars['Int']['output'];
  path: Scalars['String']['output'];
  width?: Maybe<Scalars['Int']['output']>;
};

export type AppPaginator = {
  __typename?: 'AppPaginator';
  hasMorePages: Scalars['Boolean']['output'];
  total: Scalars['Int']['output'];
};

export type Doctor = {
  __typename?: 'Doctor';
  designation?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type Establishment = {
  __typename?: 'Establishment';
  address: Scalars['String']['output'];
  doctors?: Maybe<Array<Doctor>>;
  id: Scalars['Int']['output'];
  logo?: Maybe<AppImage>;
  name: Scalars['String']['output'];
  owner: User;
  pdfSetting?: Maybe<PdfSetting>;
  phone_number: Scalars['String']['output'];
  profileImage?: Maybe<AppImage>;
  signature_image_id?: Maybe<Scalars['Int']['output']>;
};

export type EstablishmentDoctorListPaginator = {
  __typename?: 'EstablishmentDoctorListPaginator';
  data?: Maybe<Array<Doctor>>;
  paginator_info: AppPaginator;
};

export type Field = {
  __typename?: 'Field';
  /** select | multiselect | richttext | field | sub_field */
  input_type?: Maybe<Scalars['String']['output']>;
  input_type_values?: Maybe<Array<Scalars['String']['output']>>;
  name: Scalars['String']['output'];
  name_value?: Maybe<Scalars['String']['output']>;
  order: Scalars['Int']['output'];
  /** reference ranges */
  reference?: Maybe<Scalars['String']['output']>;
  reference_active?: Maybe<Scalars['Boolean']['output']>;
  reference_value?: Maybe<Scalars['String']['output']>;
  sub_fields?: Maybe<Array<SubField>>;
  unit?: Maybe<Scalars['String']['output']>;
  unit_active?: Maybe<Scalars['Boolean']['output']>;
  unit_value?: Maybe<Scalars['String']['output']>;
};

export type FieldInput = {
  id?: InputMaybe<Scalars['String']['input']>;
  input_type: FieldInputType;
  input_type_values?: InputMaybe<Array<Scalars['String']['input']>>;
  /** test name */
  name: Scalars['String']['input'];
  name_value?: InputMaybe<Scalars['String']['input']>;
  reference?: InputMaybe<Scalars['String']['input']>;
  reference_active?: InputMaybe<Scalars['Boolean']['input']>;
  reference_value?: InputMaybe<Scalars['String']['input']>;
  sub_fields?: InputMaybe<Array<SubFieldInput>>;
  unit?: InputMaybe<Scalars['String']['input']>;
  unit_active?: InputMaybe<Scalars['Boolean']['input']>;
  unit_value?: InputMaybe<Scalars['String']['input']>;
};

export enum FieldInputType {
  Multiselect = 'MULTISELECT',
  Richtext = 'RICHTEXT',
  Select = 'SELECT',
  Subfield = 'SUBFIELD',
  Textfield = 'TEXTFIELD'
}

export type GenericResponse = {
  __typename?: 'GenericResponse';
  message: Scalars['String']['output'];
};

export type GetEstablishmentListPaginator = {
  __typename?: 'GetEstablishmentListPaginator';
  data?: Maybe<Array<Establishment>>;
  paginator_info: AppPaginator;
};

export type GetTemplatePaginator = {
  __typename?: 'GetTemplatePaginator';
  data?: Maybe<Array<Template>>;
  paginator_info: AppPaginator;
};

export type GetTestReportPaginator = {
  __typename?: 'GetTestReportPaginator';
  data?: Maybe<Array<TestReport>>;
  paginator_info: AppPaginator;
};

export type LoginResponse = {
  __typename?: 'LoginResponse';
  token: Scalars['String']['output'];
  user: User;
};

export type Mutation = {
  __typename?: 'Mutation';
  addDoctor: Doctor;
  addPatient: Patient;
  createOwnerWithEstablishment: GenericResponse;
  createReport: GenericResponse;
  createTemplate: GenericResponse;
  deleteDoctor: GenericResponse;
  deletePatient: GenericResponse;
  deleteReport: GenericResponse;
  deleteTemplate: GenericResponse;
  login: LoginResponse;
  logout: GenericResponse;
  sendTestReport: GenericResponse;
  updateDoctor: GenericResponse;
  updateEstablishment: GenericResponse;
  updateMyEstablishment: GenericResponse;
  updateMyProfile: GenericResponse;
  updateOwner: GenericResponse;
  updatePatient: GenericResponse;
  updateReport: GenericResponse;
  updateTemplate: GenericResponse;
  upsertPdfSetting: GenericResponse;
};


export type MutationAddDoctorArgs = {
  designation?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};


export type MutationAddPatientArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  age: Scalars['Int']['input'];
  blood_group?: InputMaybe<Scalars['String']['input']>;
  gender: Scalars['String']['input'];
  name: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
};


export type MutationCreateOwnerWithEstablishmentArgs = {
  establishment_address?: InputMaybe<Scalars['String']['input']>;
  establishment_name: Scalars['String']['input'];
  establishment_phone_number: Scalars['String']['input'];
  logo?: InputMaybe<Scalars['Upload']['input']>;
  password: Scalars['String']['input'];
  subtitle?: InputMaybe<Scalars['String']['input']>;
  username: Scalars['String']['input'];
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateReportArgs = {
  collection_date?: InputMaybe<Scalars['DateTime']['input']>;
  doctor_id?: InputMaybe<Scalars['Int']['input']>;
  fields_input?: InputMaybe<Array<FieldInput>>;
  patient_id: Scalars['Int']['input'];
  pdf?: InputMaybe<Scalars['Upload']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  report_generation_date?: InputMaybe<Scalars['DateTime']['input']>;
  sections_input?: InputMaybe<Array<SectionInput>>;
  staff?: InputMaybe<Scalars['String']['input']>;
  template_id: Scalars['Int']['input'];
  test_date?: InputMaybe<Scalars['DateTime']['input']>;
};


export type MutationCreateTemplateArgs = {
  fields_input?: InputMaybe<Array<FieldInput>>;
  name: Scalars['String']['input'];
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput>>;
  title_color?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteDoctorArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeletePatientArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteReportArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteTemplateArgs = {
  id: Scalars['Int']['input'];
};


export type MutationLoginArgs = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationSendTestReportArgs = {
  patient_id: Scalars['Int']['input'];
  pdf: Scalars['Upload']['input'];
  report_id: Scalars['Int']['input'];
  whatsapp_number?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateDoctorArgs = {
  designation?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateEstablishmentArgs = {
  establishment_address?: InputMaybe<Scalars['String']['input']>;
  establishment_name?: InputMaybe<Scalars['String']['input']>;
  establishment_phone_number?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
  logo?: InputMaybe<Scalars['Upload']['input']>;
  subtitle?: InputMaybe<Scalars['String']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateMyEstablishmentArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  profile_image?: InputMaybe<Scalars['Upload']['input']>;
  subtitle?: InputMaybe<Scalars['String']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateMyProfileArgs = {
  password?: InputMaybe<Scalars['String']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateOwnerArgs = {
  id: Scalars['Int']['input'];
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationUpdatePatientArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  age?: InputMaybe<Scalars['Int']['input']>;
  blood_group?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateReportArgs = {
  collection_date?: InputMaybe<Scalars['DateTime']['input']>;
  doctor_id?: InputMaybe<Scalars['Int']['input']>;
  fields_input?: InputMaybe<Array<FieldInput>>;
  id: Scalars['Int']['input'];
  patient_id?: InputMaybe<Scalars['Int']['input']>;
  pdf?: InputMaybe<Scalars['Upload']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  report_generation_date?: InputMaybe<Scalars['DateTime']['input']>;
  sections_input?: InputMaybe<Array<SectionInput>>;
  staff?: InputMaybe<Scalars['String']['input']>;
  template_id?: InputMaybe<Scalars['Int']['input']>;
  test_date?: InputMaybe<Scalars['DateTime']['input']>;
};


export type MutationUpdateTemplateArgs = {
  fields_input?: InputMaybe<Array<FieldInput>>;
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput>>;
  title_color?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpsertPdfSettingArgs = {
  establishment_id?: InputMaybe<Scalars['Int']['input']>;
  footer_center?: InputMaybe<Scalars['String']['input']>;
  footer_left?: InputMaybe<Scalars['String']['input']>;
  footer_right?: InputMaybe<Scalars['String']['input']>;
  left_signature?: InputMaybe<Scalars['Upload']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  right_signature?: InputMaybe<Scalars['Upload']['input']>;
  subtitle?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  title_color?: InputMaybe<Scalars['String']['input']>;
};

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT'
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM'
}

export type Patient = {
  __typename?: 'Patient';
  address?: Maybe<Scalars['String']['output']>;
  age: Scalars['Int']['output'];
  blood_group?: Maybe<Scalars['String']['output']>;
  gender: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  owner: User;
  phone_number: Scalars['String']['output'];
};

export type PatientListPaginator = {
  __typename?: 'PatientListPaginator';
  data?: Maybe<Array<Patient>>;
  paginator_info: AppPaginator;
};

export type PdfSetting = {
  __typename?: 'PdfSetting';
  establishment?: Maybe<Establishment>;
  establishment_id: Scalars['Int']['output'];
  footer_center?: Maybe<Scalars['String']['output']>;
  footer_left?: Maybe<Scalars['String']['output']>;
  footer_right?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  leftSignature?: Maybe<AppImage>;
  logo?: Maybe<AppImage>;
  rightSignature?: Maybe<AppImage>;
  /** address and other details for pdf */
  subtitle: Scalars['String']['output'];
  /** usually the lab name */
  title: Scalars['String']['output'];
};

/** Indicates what fields are available at the top level of a query operation. */
export type Query = {
  __typename?: 'Query';
  establishmentById?: Maybe<Establishment>;
  getEstablishmentDoctorList: EstablishmentDoctorListPaginator;
  getEstablishmentList: GetEstablishmentListPaginator;
  getMe?: Maybe<User>;
  getPatientList: PatientListPaginator;
  getPdfSetting?: Maybe<PdfSetting>;
  getRecentReports?: Maybe<Array<TestReport>>;
  getReportById?: Maybe<TestReport>;
  getStats: StatsResponse;
  getTemplateBySlug: Template;
  getTemplates: GetTemplatePaginator;
  getTestReports: GetTestReportPaginator;
  getWeeklyLineChartData?: Maybe<Array<WeeklyChartData>>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryEstablishmentByIdArgs = {
  id: Scalars['Int']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetEstablishmentDoctorListArgs = {
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetEstablishmentListArgs = {
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPatientListArgs = {
  first: Scalars['Int']['input'];
  keyword?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPdfSettingArgs = {
  establishment_id?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetRecentReportsArgs = {
  first: Scalars['Int']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetReportByIdArgs = {
  id: Scalars['Int']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetStatsArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTemplateBySlugArgs = {
  slug: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTemplatesArgs = {
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTestReportsArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  patient_info?: InputMaybe<Scalars['String']['input']>;
  report_name?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};

export type Section = {
  __typename?: 'Section';
  fields?: Maybe<Array<Field>>;
  name: Scalars['String']['output'];
  name_value?: Maybe<Scalars['String']['output']>;
};

export type SectionInput = {
  fields: Array<FieldInput>;
  id?: InputMaybe<Scalars['String']['input']>;
  /** section name */
  name: Scalars['String']['input'];
  name_value?: InputMaybe<Scalars['String']['input']>;
};

/** Directions for ordering a list of records. */
export enum SortOrder {
  /** Sort records in ascending order. */
  Asc = 'ASC',
  /** Sort records in descending order. */
  Desc = 'DESC'
}

export type StatsResponse = {
  __typename?: 'StatsResponse';
  total_patient: Scalars['Int']['output'];
  total_tests_count: Scalars['Int']['output'];
};

export type SubField = {
  __typename?: 'SubField';
  children?: Maybe<Array<SubField>>;
  /** select | multiselect | richttext | field | sub_field */
  input_type: Scalars['String']['output'];
  input_type_values?: Maybe<Array<Scalars['String']['output']>>;
  name: Scalars['String']['output'];
  name_value?: Maybe<Scalars['String']['output']>;
  /** reference ranges */
  reference?: Maybe<Scalars['String']['output']>;
  reference_active?: Maybe<Scalars['Boolean']['output']>;
  reference_value?: Maybe<Scalars['String']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
  unit_active?: Maybe<Scalars['Boolean']['output']>;
  unit_value?: Maybe<Scalars['String']['output']>;
};

export type SubFieldInput = {
  children?: InputMaybe<Array<SubFieldInput>>;
  id?: InputMaybe<Scalars['String']['input']>;
  input_type: FieldInputType;
  input_type_values?: InputMaybe<Array<Scalars['String']['input']>>;
  name: Scalars['String']['input'];
  name_value?: InputMaybe<Scalars['String']['input']>;
  reference?: InputMaybe<Scalars['String']['input']>;
  reference_active?: InputMaybe<Scalars['Boolean']['input']>;
  reference_value?: InputMaybe<Scalars['String']['input']>;
  unit?: InputMaybe<Scalars['String']['input']>;
  unit_active?: InputMaybe<Scalars['Boolean']['input']>;
  unit_value?: InputMaybe<Scalars['String']['input']>;
};

export type Template = {
  __typename?: 'Template';
  establishment: Establishment;
  establishment_id: Scalars['Int']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  /** default is black */
  report_name_color: Scalars['String']['output'];
  slug: Scalars['String']['output'];
  template_data: TemplateData;
  /** default is black */
  title_color: Scalars['String']['output'];
  user: User;
  /** owner */
  user_id: Scalars['Int']['output'];
};

export type TemplateData = {
  __typename?: 'TemplateData';
  fields?: Maybe<Array<Field>>;
  sections?: Maybe<Array<Section>>;
};

export type TestReport = {
  __typename?: 'TestReport';
  collection_date?: Maybe<Scalars['DateTime']['output']>;
  created_at: Scalars['DateTime']['output'];
  doctor?: Maybe<Doctor>;
  establishment_id: Scalars['Int']['output'];
  id: Scalars['Int']['output'];
  latestWhatsAppReceipt?: Maybe<WhatsAppReceipt>;
  patient: Patient;
  patient_id: Scalars['Int']['output'];
  pdf_path?: Maybe<Scalars['String']['output']>;
  remarks?: Maybe<Scalars['String']['output']>;
  report_generation_date?: Maybe<Scalars['DateTime']['output']>;
  /** staff name */
  staff?: Maybe<Scalars['String']['output']>;
  template: Template;
  template_data: TemplateData;
  test_date?: Maybe<Scalars['DateTime']['output']>;
  updated_at: Scalars['DateTime']['output'];
  user: User;
  user_id: Scalars['Int']['output'];
  whatsappable?: Maybe<Array<Maybe<WhatsAppReceipt>>>;
};

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT'
}

/** Account of a person who utilizes this application. */
export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at: Scalars['DateTime']['output'];
  establishment?: Maybe<Establishment>;
  id: Scalars['Int']['output'];
  role: Scalars['String']['output'];
  /** When the account was last updated. */
  updated_at: Scalars['DateTime']['output'];
  username: Scalars['String']['output'];
};

export type WeeklyChartData = {
  __typename?: 'WeeklyChartData';
  template_name: Scalars['String']['output'];
  total_count: Scalars['Int']['output'];
  weekday: Scalars['String']['output'];
};

export type WhatsAppReceipt = {
  __typename?: 'WhatsAppReceipt';
  /** error if message_status is `failed` */
  error_message?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  /** accepted | read | delivered | sent | failed */
  message_status: Scalars['String']['output'];
};

export type AddDoctorMutationVariables = Exact<{
  name: Scalars['String']['input'];
  designation?: InputMaybe<Scalars['String']['input']>;
}>;


export type AddDoctorMutation = { __typename?: 'Mutation', addDoctor: { __typename?: 'Doctor', id: number, name: string } };

export type AddPatientMutationVariables = Exact<{
  name: Scalars['String']['input'];
  age: Scalars['Int']['input'];
  gender: Scalars['String']['input'];
  blood_group?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  phone_number: Scalars['String']['input'];
}>;


export type AddPatientMutation = { __typename?: 'Mutation', addPatient: { __typename?: 'Patient', id: number, name: string, phone_number: string, age: number, gender: string, blood_group?: string | null, address?: string | null } };

export type CreateOwnerWithEstablishmentMutationVariables = Exact<{
  username: Scalars['String']['input'];
  password: Scalars['String']['input'];
  establishment_name: Scalars['String']['input'];
  establishment_phone_number: Scalars['String']['input'];
  establishment_address?: InputMaybe<Scalars['String']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
}>;


export type CreateOwnerWithEstablishmentMutation = { __typename?: 'Mutation', createOwnerWithEstablishment: { __typename?: 'GenericResponse', message: string } };

export type CreateReportMutationVariables = Exact<{
  template_id: Scalars['Int']['input'];
  sections_input?: InputMaybe<Array<SectionInput> | SectionInput>;
  fields_input?: InputMaybe<Array<FieldInput> | FieldInput>;
  pdf?: InputMaybe<Scalars['Upload']['input']>;
  patient_id: Scalars['Int']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
  staff?: InputMaybe<Scalars['String']['input']>;
  collection_date?: InputMaybe<Scalars['DateTime']['input']>;
  test_date?: InputMaybe<Scalars['DateTime']['input']>;
  report_generation_date?: InputMaybe<Scalars['DateTime']['input']>;
  doctor_id?: InputMaybe<Scalars['Int']['input']>;
}>;


export type CreateReportMutation = { __typename?: 'Mutation', createReport: { __typename?: 'GenericResponse', message: string } };

export type CreateTemplateMutationVariables = Exact<{
  name: Scalars['String']['input'];
  title_color?: InputMaybe<Scalars['String']['input']>;
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput> | SectionInput>;
  fields_input?: InputMaybe<Array<FieldInput> | FieldInput>;
}>;


export type CreateTemplateMutation = { __typename?: 'Mutation', createTemplate: { __typename?: 'GenericResponse', message: string } };

export type DeleteDoctorMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeleteDoctorMutation = { __typename?: 'Mutation', deleteDoctor: { __typename?: 'GenericResponse', message: string } };

export type DeletePatientMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeletePatientMutation = { __typename?: 'Mutation', deletePatient: { __typename?: 'GenericResponse', message: string } };

export type DeleteReportMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeleteReportMutation = { __typename?: 'Mutation', deleteReport: { __typename?: 'GenericResponse', message: string } };

export type DeleteTemplateMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeleteTemplateMutation = { __typename?: 'Mutation', deleteTemplate: { __typename?: 'GenericResponse', message: string } };

export type LoginMutationVariables = Exact<{
  username: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type LoginMutation = { __typename?: 'Mutation', login: { __typename?: 'LoginResponse', token: string, user: { __typename?: 'User', id: number, username: string, role: string } } };

export type LogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type LogoutMutation = { __typename?: 'Mutation', logout: { __typename?: 'GenericResponse', message: string } };

export type SendTestReportMutationVariables = Exact<{
  report_id: Scalars['Int']['input'];
  patient_id: Scalars['Int']['input'];
  whatsapp_number?: InputMaybe<Scalars['String']['input']>;
  pdf: Scalars['Upload']['input'];
}>;


export type SendTestReportMutation = { __typename?: 'Mutation', sendTestReport: { __typename?: 'GenericResponse', message: string } };

export type UpdateDoctorMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  designation?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateDoctorMutation = { __typename?: 'Mutation', updateDoctor: { __typename?: 'GenericResponse', message: string } };

export type UpdateEstablishmentMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  establishment_name?: InputMaybe<Scalars['String']['input']>;
  establishment_phone_number?: InputMaybe<Scalars['String']['input']>;
  establishment_address?: InputMaybe<Scalars['String']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
}>;


export type UpdateEstablishmentMutation = { __typename?: 'Mutation', updateEstablishment: { __typename?: 'GenericResponse', message: string } };

export type UpdateMyEstablishmentMutationVariables = Exact<{
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateMyEstablishmentMutation = { __typename?: 'Mutation', updateMyEstablishment: { __typename?: 'GenericResponse', message: string } };

export type UpdateMyProfileMutationVariables = Exact<{
  username?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateMyProfileMutation = { __typename?: 'Mutation', updateMyProfile: { __typename?: 'GenericResponse', message: string } };

export type UpdateOwnerMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  username: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type UpdateOwnerMutation = { __typename?: 'Mutation', updateOwner: { __typename?: 'GenericResponse', message: string } };

export type UpdatePatientMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  age?: InputMaybe<Scalars['Int']['input']>;
  gender?: InputMaybe<Scalars['String']['input']>;
  blood_group?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdatePatientMutation = { __typename?: 'Mutation', updatePatient: { __typename?: 'GenericResponse', message: string } };

export type UpdateReportMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
  staff?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput> | SectionInput>;
  fields_input?: InputMaybe<Array<FieldInput> | FieldInput>;
  template_id?: InputMaybe<Scalars['Int']['input']>;
  patient_id?: InputMaybe<Scalars['Int']['input']>;
  collection_date?: InputMaybe<Scalars['DateTime']['input']>;
  test_date?: InputMaybe<Scalars['DateTime']['input']>;
  report_generation_date?: InputMaybe<Scalars['DateTime']['input']>;
  doctor_id?: InputMaybe<Scalars['Int']['input']>;
}>;


export type UpdateReportMutation = { __typename?: 'Mutation', updateReport: { __typename?: 'GenericResponse', message: string } };

export type UpdateTemplateMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  title_color?: InputMaybe<Scalars['String']['input']>;
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput> | SectionInput>;
  fields_input?: InputMaybe<Array<FieldInput> | FieldInput>;
}>;


export type UpdateTemplateMutation = { __typename?: 'Mutation', updateTemplate: { __typename?: 'GenericResponse', message: string } };

export type UpsertPdfSettingMutationVariables = Exact<{
  establishment_id?: InputMaybe<Scalars['Int']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  subtitle?: InputMaybe<Scalars['String']['input']>;
  footer_left?: InputMaybe<Scalars['String']['input']>;
  footer_right?: InputMaybe<Scalars['String']['input']>;
  footer_center?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
  left_signature?: InputMaybe<Scalars['Upload']['input']>;
  right_signature?: InputMaybe<Scalars['Upload']['input']>;
}>;


export type UpsertPdfSettingMutation = { __typename?: 'Mutation', upsertPdfSetting: { __typename?: 'GenericResponse', message: string } };

export type GetEstablishmentDoctorListQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetEstablishmentDoctorListQuery = { __typename?: 'Query', getEstablishmentDoctorList: { __typename?: 'EstablishmentDoctorListPaginator', data?: Array<{ __typename?: 'Doctor', id: number, name: string, designation?: string | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetEstablishmentListQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetEstablishmentListQuery = { __typename?: 'Query', getEstablishmentList: { __typename?: 'GetEstablishmentListPaginator', data?: Array<{ __typename?: 'Establishment', id: number, name: string, address: string, phone_number: string, owner: { __typename?: 'User', id: number, username: string } }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetMeQueryVariables = Exact<{ [key: string]: never; }>;


export type GetMeQuery = { __typename?: 'Query', getMe?: { __typename?: 'User', id: number, username: string, establishment?: { __typename?: 'Establishment', id: number, name: string, address: string, phone_number: string, logo?: { __typename?: 'AppImage', path: string } | null } | null } | null };

export type GetPatientListQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetPatientListQuery = { __typename?: 'Query', getPatientList: { __typename?: 'PatientListPaginator', data?: Array<{ __typename?: 'Patient', id: number, name: string, age: number, gender: string, blood_group?: string | null, address?: string | null, phone_number: string }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetPdfSettingQueryVariables = Exact<{ [key: string]: never; }>;


export type GetPdfSettingQuery = { __typename?: 'Query', getPdfSetting?: { __typename?: 'PdfSetting', id: number, establishment_id: number, title: string, subtitle: string, footer_left?: string | null, footer_right?: string | null, footer_center?: string | null, logo?: { __typename?: 'AppImage', id: number, path: string, hash: string, order: number } | null, leftSignature?: { __typename?: 'AppImage', id: number, path: string, hash: string } | null, rightSignature?: { __typename?: 'AppImage', id: number, path: string, hash: string } | null } | null };

export type GetRecentReportsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
}>;


export type GetRecentReportsQuery = { __typename?: 'Query', getRecentReports?: Array<{ __typename?: 'TestReport', id: number, patient: { __typename?: 'Patient', id: number, name: string }, template: { __typename?: 'Template', id: number, name: string } }> | null };

export type GetReportByIdQueryVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type GetReportByIdQuery = { __typename?: 'Query', getReportById?: { __typename?: 'TestReport', id: number, remarks?: string | null, staff?: string | null, collection_date?: any | null, test_date?: any | null, report_generation_date?: any | null, patient: { __typename?: 'Patient', id: number, name: string, phone_number: string, age: number, gender: string, address?: string | null, blood_group?: string | null }, template_data: { __typename?: 'TemplateData', fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, reference?: string | null, reference_value?: string | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null }> | null }> | null, sections?: Array<{ __typename?: 'Section', name: string, name_value?: string | null, fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_active?: boolean | null, reference_value?: string | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null }> | null }> | null }> | null }, template: { __typename?: 'Template', id: number, name: string, title_color: string, report_name_color: string, template_data: { __typename?: 'TemplateData', fields?: Array<{ __typename?: 'Field', name: string, unit?: string | null, reference?: string | null, input_type?: string | null }> | null, sections?: Array<{ __typename?: 'Section', name: string, name_value?: string | null, fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_active?: boolean | null, reference_value?: string | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null }> | null }> | null }> | null } }, doctor?: { __typename?: 'Doctor', id: number, name: string } | null } | null };

export type GetStatsQueryVariables = Exact<{
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetStatsQuery = { __typename?: 'Query', getStats: { __typename?: 'StatsResponse', total_patient: number, total_tests_count: number } };

export type GetTemplateBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type GetTemplateBySlugQuery = { __typename?: 'Query', getTemplateBySlug: { __typename?: 'Template', id: number, name: string, title_color: string, report_name_color: string, template_data: { __typename?: 'TemplateData', fields?: Array<{ __typename?: 'Field', name: string, unit?: string | null, reference?: string | null, input_type?: string | null }> | null, sections?: Array<{ __typename?: 'Section', name: string, fields?: Array<{ __typename?: 'Field', name: string, unit?: string | null, unit_active?: boolean | null, reference?: string | null, reference_active?: boolean | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, input_type: string, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null }> | null }> | null }> | null } } };

export type GetTemplatesQueryVariables = Exact<{
  name?: InputMaybe<Scalars['String']['input']>;
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetTemplatesQuery = { __typename?: 'Query', getTemplates: { __typename?: 'GetTemplatePaginator', data?: Array<{ __typename?: 'Template', id: number, name: string, slug: string }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetTestReportsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  report_name?: InputMaybe<Scalars['String']['input']>;
  patient_info?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetTestReportsQuery = { __typename?: 'Query', getTestReports: { __typename?: 'GetTestReportPaginator', data?: Array<{ __typename?: 'TestReport', id: number, created_at: any, remarks?: string | null, staff?: string | null, collection_date?: any | null, report_generation_date?: any | null, test_date?: any | null, patient: { __typename?: 'Patient', id: number, name: string, phone_number: string, address?: string | null, age: number, gender: string, blood_group?: string | null }, template: { __typename?: 'Template', id: number, name: string, slug: string, title_color: string, report_name_color: string }, template_data: { __typename?: 'TemplateData', fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, reference?: string | null, reference_value?: string | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null }> | null }> | null, sections?: Array<{ __typename?: 'Section', name: string, name_value?: string | null, fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null }> | null }> | null }> | null }, doctor?: { __typename?: 'Doctor', id: number, name: string } | null, latestWhatsAppReceipt?: { __typename?: 'WhatsAppReceipt', message_status: string, error_message?: string | null } | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetWeeklyLineChartDataQueryVariables = Exact<{ [key: string]: never; }>;


export type GetWeeklyLineChartDataQuery = { __typename?: 'Query', getWeeklyLineChartData?: Array<{ __typename?: 'WeeklyChartData', template_name: string, total_count: number, weekday: string }> | null };


export const AddDoctorDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddDoctor"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"designation"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addDoctor"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"designation"},"value":{"kind":"Variable","name":{"kind":"Name","value":"designation"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]} as unknown as DocumentNode<AddDoctorMutation, AddDoctorMutationVariables>;
export const AddPatientDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddPatient"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"age"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"gender"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"blood_group"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addPatient"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"age"},"value":{"kind":"Variable","name":{"kind":"Name","value":"age"}}},{"kind":"Argument","name":{"kind":"Name","value":"gender"},"value":{"kind":"Variable","name":{"kind":"Name","value":"gender"}}},{"kind":"Argument","name":{"kind":"Name","value":"blood_group"},"value":{"kind":"Variable","name":{"kind":"Name","value":"blood_group"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"age"}},{"kind":"Field","name":{"kind":"Name","value":"gender"}},{"kind":"Field","name":{"kind":"Name","value":"blood_group"}},{"kind":"Field","name":{"kind":"Name","value":"address"}}]}}]}}]} as unknown as DocumentNode<AddPatientMutation, AddPatientMutationVariables>;
export const CreateOwnerWithEstablishmentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateOwnerWithEstablishment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"establishment_name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"establishment_phone_number"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"establishment_address"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"wa_phone_number_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"logo"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createOwnerWithEstablishment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}},{"kind":"Argument","name":{"kind":"Name","value":"establishment_name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"establishment_name"}}},{"kind":"Argument","name":{"kind":"Name","value":"establishment_phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"establishment_phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"establishment_address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"establishment_address"}}},{"kind":"Argument","name":{"kind":"Name","value":"wa_phone_number_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"wa_phone_number_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"logo"},"value":{"kind":"Variable","name":{"kind":"Name","value":"logo"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<CreateOwnerWithEstablishmentMutation, CreateOwnerWithEstablishmentMutationVariables>;
export const CreateReportDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateReport"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"template_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sections_input"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"SectionInput"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fields_input"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"FieldInput"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pdf"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"patient_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"staff"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"collection_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"test_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"report_generation_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"doctor_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createReport"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"template_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"template_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"sections_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sections_input"}}},{"kind":"Argument","name":{"kind":"Name","value":"fields_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fields_input"}}},{"kind":"Argument","name":{"kind":"Name","value":"pdf"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pdf"}}},{"kind":"Argument","name":{"kind":"Name","value":"patient_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patient_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"remarks"},"value":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}}},{"kind":"Argument","name":{"kind":"Name","value":"staff"},"value":{"kind":"Variable","name":{"kind":"Name","value":"staff"}}},{"kind":"Argument","name":{"kind":"Name","value":"collection_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"collection_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"test_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"test_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"report_generation_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"report_generation_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"doctor_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"doctor_id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<CreateReportMutation, CreateReportMutationVariables>;
export const CreateTemplateDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateTemplate"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"title_color"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"report_name_color"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sections_input"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"SectionInput"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fields_input"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"FieldInput"}}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createTemplate"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"title_color"},"value":{"kind":"Variable","name":{"kind":"Name","value":"title_color"}}},{"kind":"Argument","name":{"kind":"Name","value":"report_name_color"},"value":{"kind":"Variable","name":{"kind":"Name","value":"report_name_color"}}},{"kind":"Argument","name":{"kind":"Name","value":"sections_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sections_input"}}},{"kind":"Argument","name":{"kind":"Name","value":"fields_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fields_input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<CreateTemplateMutation, CreateTemplateMutationVariables>;
export const DeleteDoctorDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteDoctor"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteDoctor"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<DeleteDoctorMutation, DeleteDoctorMutationVariables>;
export const DeletePatientDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeletePatient"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deletePatient"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<DeletePatientMutation, DeletePatientMutationVariables>;
export const DeleteReportDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteReport"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteReport"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<DeleteReportMutation, DeleteReportMutationVariables>;
export const DeleteTemplateDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteTemplate"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteTemplate"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<DeleteTemplateMutation, DeleteTemplateMutationVariables>;
export const LoginDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"Login"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"login"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"token"}},{"kind":"Field","name":{"kind":"Name","value":"user"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"role"}}]}}]}}]}}]} as unknown as DocumentNode<LoginMutation, LoginMutationVariables>;
export const LogoutDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"Logout"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"logout"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<LogoutMutation, LogoutMutationVariables>;
export const SendTestReportDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"SendTestReport"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"report_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"patient_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"whatsapp_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pdf"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"sendTestReport"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"report_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"report_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"patient_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patient_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"whatsapp_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"whatsapp_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"pdf"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pdf"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<SendTestReportMutation, SendTestReportMutationVariables>;
export const UpdateDoctorDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateDoctor"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"designation"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateDoctor"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"designation"},"value":{"kind":"Variable","name":{"kind":"Name","value":"designation"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpdateDoctorMutation, UpdateDoctorMutationVariables>;
export const UpdateEstablishmentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateEstablishment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"establishment_name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"establishment_phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"establishment_address"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"wa_phone_number_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"logo"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateEstablishment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"establishment_name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"establishment_name"}}},{"kind":"Argument","name":{"kind":"Name","value":"establishment_phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"establishment_phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"establishment_address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"establishment_address"}}},{"kind":"Argument","name":{"kind":"Name","value":"wa_phone_number_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"wa_phone_number_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"logo"},"value":{"kind":"Variable","name":{"kind":"Name","value":"logo"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpdateEstablishmentMutation, UpdateEstablishmentMutationVariables>;
export const UpdateMyEstablishmentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateMyEstablishment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"logo"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"wa_phone_number_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateMyEstablishment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"wa_phone_number_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"wa_phone_number_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"logo"},"value":{"kind":"Variable","name":{"kind":"Name","value":"logo"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpdateMyEstablishmentMutation, UpdateMyEstablishmentMutationVariables>;
export const UpdateMyProfileDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateMyProfile"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateMyProfile"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpdateMyProfileMutation, UpdateMyProfileMutationVariables>;
export const UpdateOwnerDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateOwner"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateOwner"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpdateOwnerMutation, UpdateOwnerMutationVariables>;
export const UpdatePatientDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdatePatient"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"age"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"gender"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"blood_group"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updatePatient"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"age"},"value":{"kind":"Variable","name":{"kind":"Name","value":"age"}}},{"kind":"Argument","name":{"kind":"Name","value":"gender"},"value":{"kind":"Variable","name":{"kind":"Name","value":"gender"}}},{"kind":"Argument","name":{"kind":"Name","value":"blood_group"},"value":{"kind":"Variable","name":{"kind":"Name","value":"blood_group"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpdatePatientMutation, UpdatePatientMutationVariables>;
export const UpdateReportDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateReport"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"staff"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sections_input"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"SectionInput"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fields_input"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"FieldInput"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"template_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"patient_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"collection_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"test_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"report_generation_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"doctor_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateReport"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"remarks"},"value":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}}},{"kind":"Argument","name":{"kind":"Name","value":"staff"},"value":{"kind":"Variable","name":{"kind":"Name","value":"staff"}}},{"kind":"Argument","name":{"kind":"Name","value":"sections_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sections_input"}}},{"kind":"Argument","name":{"kind":"Name","value":"fields_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fields_input"}}},{"kind":"Argument","name":{"kind":"Name","value":"template_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"template_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"patient_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patient_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"collection_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"collection_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"test_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"test_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"report_generation_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"report_generation_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"doctor_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"doctor_id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpdateReportMutation, UpdateReportMutationVariables>;
export const UpdateTemplateDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateTemplate"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"title_color"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"report_name_color"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sections_input"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"SectionInput"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"fields_input"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"FieldInput"}}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateTemplate"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"title_color"},"value":{"kind":"Variable","name":{"kind":"Name","value":"title_color"}}},{"kind":"Argument","name":{"kind":"Name","value":"report_name_color"},"value":{"kind":"Variable","name":{"kind":"Name","value":"report_name_color"}}},{"kind":"Argument","name":{"kind":"Name","value":"sections_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sections_input"}}},{"kind":"Argument","name":{"kind":"Name","value":"fields_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"fields_input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpdateTemplateMutation, UpdateTemplateMutationVariables>;
export const UpsertPdfSettingDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpsertPdfSetting"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"establishment_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"title"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"subtitle"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"footer_left"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"footer_right"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"footer_center"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"logo"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"left_signature"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"right_signature"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Upload"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"upsertPdfSetting"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"establishment_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"establishment_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"title"},"value":{"kind":"Variable","name":{"kind":"Name","value":"title"}}},{"kind":"Argument","name":{"kind":"Name","value":"subtitle"},"value":{"kind":"Variable","name":{"kind":"Name","value":"subtitle"}}},{"kind":"Argument","name":{"kind":"Name","value":"footer_left"},"value":{"kind":"Variable","name":{"kind":"Name","value":"footer_left"}}},{"kind":"Argument","name":{"kind":"Name","value":"footer_right"},"value":{"kind":"Variable","name":{"kind":"Name","value":"footer_right"}}},{"kind":"Argument","name":{"kind":"Name","value":"footer_center"},"value":{"kind":"Variable","name":{"kind":"Name","value":"footer_center"}}},{"kind":"Argument","name":{"kind":"Name","value":"logo"},"value":{"kind":"Variable","name":{"kind":"Name","value":"logo"}}},{"kind":"Argument","name":{"kind":"Name","value":"left_signature"},"value":{"kind":"Variable","name":{"kind":"Name","value":"left_signature"}}},{"kind":"Argument","name":{"kind":"Name","value":"right_signature"},"value":{"kind":"Variable","name":{"kind":"Name","value":"right_signature"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}}]}}]}}]} as unknown as DocumentNode<UpsertPdfSettingMutation, UpsertPdfSettingMutationVariables>;
export const GetEstablishmentDoctorListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetEstablishmentDoctorList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getEstablishmentDoctorList"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"designation"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}}]}}]}}]}}]} as unknown as DocumentNode<GetEstablishmentDoctorListQuery, GetEstablishmentDoctorListQueryVariables>;
export const GetEstablishmentListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetEstablishmentList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getEstablishmentList"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"owner"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"username"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}}]}}]}}]}}]} as unknown as DocumentNode<GetEstablishmentListQuery, GetEstablishmentListQueryVariables>;
export const GetMeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetMe"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getMe"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"establishment"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"logo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetMeQuery, GetMeQueryVariables>;
export const GetPatientListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPatientList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPatientList"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"keyword"},"value":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"age"}},{"kind":"Field","name":{"kind":"Name","value":"gender"}},{"kind":"Field","name":{"kind":"Name","value":"blood_group"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}}]}}]}}]}}]} as unknown as DocumentNode<GetPatientListQuery, GetPatientListQueryVariables>;
export const GetPdfSettingDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPdfSetting"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPdfSetting"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"establishment_id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"subtitle"}},{"kind":"Field","name":{"kind":"Name","value":"footer_left"}},{"kind":"Field","name":{"kind":"Name","value":"footer_right"}},{"kind":"Field","name":{"kind":"Name","value":"footer_center"}},{"kind":"Field","name":{"kind":"Name","value":"logo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}},{"kind":"Field","name":{"kind":"Name","value":"order"}}]}},{"kind":"Field","name":{"kind":"Name","value":"leftSignature"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}},{"kind":"Field","name":{"kind":"Name","value":"rightSignature"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}},{"kind":"Field","name":{"kind":"Name","value":"hash"}}]}}]}}]}}]} as unknown as DocumentNode<GetPdfSettingQuery, GetPdfSettingQueryVariables>;
export const GetRecentReportsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetRecentReports"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getRecentReports"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"patient"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"template"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]}}]} as unknown as DocumentNode<GetRecentReportsQuery, GetRecentReportsQueryVariables>;
export const GetReportByIdDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetReportById"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getReportById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"remarks"}},{"kind":"Field","name":{"kind":"Name","value":"staff"}},{"kind":"Field","name":{"kind":"Name","value":"collection_date"}},{"kind":"Field","name":{"kind":"Name","value":"test_date"}},{"kind":"Field","name":{"kind":"Name","value":"report_generation_date"}},{"kind":"Field","name":{"kind":"Name","value":"patient"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"age"}},{"kind":"Field","name":{"kind":"Name","value":"gender"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"blood_group"}}]}},{"kind":"Field","name":{"kind":"Name","value":"template_data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_value"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_value"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}},{"kind":"Field","name":{"kind":"Name","value":"sub_fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"sections"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference_value"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}},{"kind":"Field","name":{"kind":"Name","value":"sub_fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_value"}},{"kind":"Field","name":{"kind":"Name","value":"reference_active"}}]}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"template"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"title_color"}},{"kind":"Field","name":{"kind":"Name","value":"report_name_color"}},{"kind":"Field","name":{"kind":"Name","value":"template_data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"sections"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference_value"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}},{"kind":"Field","name":{"kind":"Name","value":"sub_fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_value"}},{"kind":"Field","name":{"kind":"Name","value":"reference_active"}}]}}]}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"doctor"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]}}]} as unknown as DocumentNode<GetReportByIdQuery, GetReportByIdQueryVariables>;
export const GetStatsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetStats"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getStats"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"start_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"end_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total_patient"}},{"kind":"Field","name":{"kind":"Name","value":"total_tests_count"}}]}}]}}]} as unknown as DocumentNode<GetStatsQuery, GetStatsQueryVariables>;
export const GetTemplateBySlugDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetTemplateBySlug"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"slug"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getTemplateBySlug"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"slug"},"value":{"kind":"Variable","name":{"kind":"Name","value":"slug"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"title_color"}},{"kind":"Field","name":{"kind":"Name","value":"report_name_color"}},{"kind":"Field","name":{"kind":"Name","value":"template_data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"sections"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_active"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}},{"kind":"Field","name":{"kind":"Name","value":"sub_fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_value"}},{"kind":"Field","name":{"kind":"Name","value":"reference_active"}}]}}]}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetTemplateBySlugQuery, GetTemplateBySlugQueryVariables>;
export const GetTemplatesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetTemplates"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getTemplates"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}}]}}]}}]}}]} as unknown as DocumentNode<GetTemplatesQuery, GetTemplatesQueryVariables>;
export const GetTestReportsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetTestReports"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"report_name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"patient_info"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getTestReports"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"start_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"end_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"report_name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"report_name"}}},{"kind":"Argument","name":{"kind":"Name","value":"patient_info"},"value":{"kind":"Variable","name":{"kind":"Name","value":"patient_info"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"patient"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"age"}},{"kind":"Field","name":{"kind":"Name","value":"gender"}},{"kind":"Field","name":{"kind":"Name","value":"blood_group"}}]}},{"kind":"Field","name":{"kind":"Name","value":"template"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"title_color"}},{"kind":"Field","name":{"kind":"Name","value":"report_name_color"}}]}},{"kind":"Field","name":{"kind":"Name","value":"template_data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_value"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_value"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}},{"kind":"Field","name":{"kind":"Name","value":"sub_fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"sections"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_value"}},{"kind":"Field","name":{"kind":"Name","value":"reference_active"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}},{"kind":"Field","name":{"kind":"Name","value":"sub_fields"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"name_value"}},{"kind":"Field","name":{"kind":"Name","value":"input_type"}},{"kind":"Field","name":{"kind":"Name","value":"input_type_values"}},{"kind":"Field","name":{"kind":"Name","value":"unit"}},{"kind":"Field","name":{"kind":"Name","value":"unit_value"}},{"kind":"Field","name":{"kind":"Name","value":"unit_active"}},{"kind":"Field","name":{"kind":"Name","value":"reference"}},{"kind":"Field","name":{"kind":"Name","value":"reference_value"}},{"kind":"Field","name":{"kind":"Name","value":"reference_active"}}]}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"doctor"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"latestWhatsAppReceipt"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message_status"}},{"kind":"Field","name":{"kind":"Name","value":"error_message"}}]}},{"kind":"Field","name":{"kind":"Name","value":"created_at"}},{"kind":"Field","name":{"kind":"Name","value":"remarks"}},{"kind":"Field","name":{"kind":"Name","value":"staff"}},{"kind":"Field","name":{"kind":"Name","value":"collection_date"}},{"kind":"Field","name":{"kind":"Name","value":"report_generation_date"}},{"kind":"Field","name":{"kind":"Name","value":"test_date"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}}]}}]}}]}}]} as unknown as DocumentNode<GetTestReportsQuery, GetTestReportsQueryVariables>;
export const GetWeeklyLineChartDataDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetWeeklyLineChartData"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getWeeklyLineChartData"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"template_name"}},{"kind":"Field","name":{"kind":"Name","value":"total_count"}},{"kind":"Field","name":{"kind":"Name","value":"weekday"}}]}}]}}]} as unknown as DocumentNode<GetWeeklyLineChartDataQuery, GetWeeklyLineChartDataQueryVariables>;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`. */
  DateTime: { input: any; output: any; }
  Upload: { input: any; output: any; }
};

export type AppImage = {
  __typename?: 'AppImage';
  hash: Scalars['String']['output'];
  height?: Maybe<Scalars['Int']['output']>;
  id: Scalars['Int']['output'];
  order: Scalars['Int']['output'];
  path: Scalars['String']['output'];
  width?: Maybe<Scalars['Int']['output']>;
};

export type AppPaginator = {
  __typename?: 'AppPaginator';
  hasMorePages: Scalars['Boolean']['output'];
  total: Scalars['Int']['output'];
};

export type Doctor = {
  __typename?: 'Doctor';
  designation?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type Establishment = {
  __typename?: 'Establishment';
  address: Scalars['String']['output'];
  doctors?: Maybe<Array<Doctor>>;
  id: Scalars['Int']['output'];
  logo?: Maybe<AppImage>;
  name: Scalars['String']['output'];
  owner: User;
  pdfSetting?: Maybe<PdfSetting>;
  phone_number: Scalars['String']['output'];
  profileImage?: Maybe<AppImage>;
  signature_image_id?: Maybe<Scalars['Int']['output']>;
};

export type EstablishmentDoctorListPaginator = {
  __typename?: 'EstablishmentDoctorListPaginator';
  data?: Maybe<Array<Doctor>>;
  paginator_info: AppPaginator;
};

export type Field = {
  __typename?: 'Field';
  /** select | multiselect | richttext | field | sub_field */
  input_type?: Maybe<Scalars['String']['output']>;
  input_type_values?: Maybe<Array<Scalars['String']['output']>>;
  name: Scalars['String']['output'];
  name_value?: Maybe<Scalars['String']['output']>;
  order: Scalars['Int']['output'];
  /** reference ranges */
  reference?: Maybe<Scalars['String']['output']>;
  reference_active?: Maybe<Scalars['Boolean']['output']>;
  reference_value?: Maybe<Scalars['String']['output']>;
  sub_fields?: Maybe<Array<SubField>>;
  unit?: Maybe<Scalars['String']['output']>;
  unit_active?: Maybe<Scalars['Boolean']['output']>;
  unit_value?: Maybe<Scalars['String']['output']>;
};

export type FieldInput = {
  id?: InputMaybe<Scalars['String']['input']>;
  input_type: FieldInputType;
  input_type_values?: InputMaybe<Array<Scalars['String']['input']>>;
  /** test name */
  name: Scalars['String']['input'];
  name_value?: InputMaybe<Scalars['String']['input']>;
  reference?: InputMaybe<Scalars['String']['input']>;
  reference_active?: InputMaybe<Scalars['Boolean']['input']>;
  reference_value?: InputMaybe<Scalars['String']['input']>;
  sub_fields?: InputMaybe<Array<SubFieldInput>>;
  unit?: InputMaybe<Scalars['String']['input']>;
  unit_active?: InputMaybe<Scalars['Boolean']['input']>;
  unit_value?: InputMaybe<Scalars['String']['input']>;
};

export enum FieldInputType {
  Multiselect = 'MULTISELECT',
  Richtext = 'RICHTEXT',
  Select = 'SELECT',
  Subfield = 'SUBFIELD',
  Textfield = 'TEXTFIELD'
}

export type GenericResponse = {
  __typename?: 'GenericResponse';
  message: Scalars['String']['output'];
};

export type GetEstablishmentListPaginator = {
  __typename?: 'GetEstablishmentListPaginator';
  data?: Maybe<Array<Establishment>>;
  paginator_info: AppPaginator;
};

export type GetTemplatePaginator = {
  __typename?: 'GetTemplatePaginator';
  data?: Maybe<Array<Template>>;
  paginator_info: AppPaginator;
};

export type GetTestReportPaginator = {
  __typename?: 'GetTestReportPaginator';
  data?: Maybe<Array<TestReport>>;
  paginator_info: AppPaginator;
};

export type LoginResponse = {
  __typename?: 'LoginResponse';
  token: Scalars['String']['output'];
  user: User;
};

export type Mutation = {
  __typename?: 'Mutation';
  addDoctor: Doctor;
  addPatient: Patient;
  createOwnerWithEstablishment: GenericResponse;
  createReport: GenericResponse;
  createTemplate: GenericResponse;
  deleteDoctor: GenericResponse;
  deletePatient: GenericResponse;
  deleteReport: GenericResponse;
  deleteTemplate: GenericResponse;
  login: LoginResponse;
  logout: GenericResponse;
  sendTestReport: GenericResponse;
  updateDoctor: GenericResponse;
  updateEstablishment: GenericResponse;
  updateMyEstablishment: GenericResponse;
  updateMyProfile: GenericResponse;
  updateOwner: GenericResponse;
  updatePatient: GenericResponse;
  updateReport: GenericResponse;
  updateTemplate: GenericResponse;
  upsertPdfSetting: GenericResponse;
};


export type MutationAddDoctorArgs = {
  designation?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};


export type MutationAddPatientArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  age: Scalars['Int']['input'];
  blood_group?: InputMaybe<Scalars['String']['input']>;
  gender: Scalars['String']['input'];
  name: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
};


export type MutationCreateOwnerWithEstablishmentArgs = {
  establishment_address?: InputMaybe<Scalars['String']['input']>;
  establishment_name: Scalars['String']['input'];
  establishment_phone_number: Scalars['String']['input'];
  logo?: InputMaybe<Scalars['Upload']['input']>;
  password: Scalars['String']['input'];
  subtitle?: InputMaybe<Scalars['String']['input']>;
  username: Scalars['String']['input'];
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateReportArgs = {
  collection_date?: InputMaybe<Scalars['DateTime']['input']>;
  doctor_id?: InputMaybe<Scalars['Int']['input']>;
  fields_input?: InputMaybe<Array<FieldInput>>;
  patient_id: Scalars['Int']['input'];
  pdf?: InputMaybe<Scalars['Upload']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  report_generation_date?: InputMaybe<Scalars['DateTime']['input']>;
  sections_input?: InputMaybe<Array<SectionInput>>;
  staff?: InputMaybe<Scalars['String']['input']>;
  template_id: Scalars['Int']['input'];
  test_date?: InputMaybe<Scalars['DateTime']['input']>;
};


export type MutationCreateTemplateArgs = {
  fields_input?: InputMaybe<Array<FieldInput>>;
  name: Scalars['String']['input'];
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput>>;
  title_color?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteDoctorArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeletePatientArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteReportArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteTemplateArgs = {
  id: Scalars['Int']['input'];
};


export type MutationLoginArgs = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationSendTestReportArgs = {
  patient_id: Scalars['Int']['input'];
  pdf: Scalars['Upload']['input'];
  report_id: Scalars['Int']['input'];
  whatsapp_number?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateDoctorArgs = {
  designation?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateEstablishmentArgs = {
  establishment_address?: InputMaybe<Scalars['String']['input']>;
  establishment_name?: InputMaybe<Scalars['String']['input']>;
  establishment_phone_number?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
  logo?: InputMaybe<Scalars['Upload']['input']>;
  subtitle?: InputMaybe<Scalars['String']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateMyEstablishmentArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  profile_image?: InputMaybe<Scalars['Upload']['input']>;
  subtitle?: InputMaybe<Scalars['String']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateMyProfileArgs = {
  password?: InputMaybe<Scalars['String']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateOwnerArgs = {
  id: Scalars['Int']['input'];
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationUpdatePatientArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  age?: InputMaybe<Scalars['Int']['input']>;
  blood_group?: InputMaybe<Scalars['String']['input']>;
  gender?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateReportArgs = {
  collection_date?: InputMaybe<Scalars['DateTime']['input']>;
  doctor_id?: InputMaybe<Scalars['Int']['input']>;
  fields_input?: InputMaybe<Array<FieldInput>>;
  id: Scalars['Int']['input'];
  patient_id?: InputMaybe<Scalars['Int']['input']>;
  pdf?: InputMaybe<Scalars['Upload']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  report_generation_date?: InputMaybe<Scalars['DateTime']['input']>;
  sections_input?: InputMaybe<Array<SectionInput>>;
  staff?: InputMaybe<Scalars['String']['input']>;
  template_id?: InputMaybe<Scalars['Int']['input']>;
  test_date?: InputMaybe<Scalars['DateTime']['input']>;
};


export type MutationUpdateTemplateArgs = {
  fields_input?: InputMaybe<Array<FieldInput>>;
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput>>;
  title_color?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpsertPdfSettingArgs = {
  establishment_id?: InputMaybe<Scalars['Int']['input']>;
  footer_center?: InputMaybe<Scalars['String']['input']>;
  footer_left?: InputMaybe<Scalars['String']['input']>;
  footer_right?: InputMaybe<Scalars['String']['input']>;
  left_signature?: InputMaybe<Scalars['Upload']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  right_signature?: InputMaybe<Scalars['Upload']['input']>;
  subtitle?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  title_color?: InputMaybe<Scalars['String']['input']>;
};

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT'
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM'
}

export type Patient = {
  __typename?: 'Patient';
  address?: Maybe<Scalars['String']['output']>;
  age: Scalars['Int']['output'];
  blood_group?: Maybe<Scalars['String']['output']>;
  gender: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  owner: User;
  phone_number: Scalars['String']['output'];
};

export type PatientListPaginator = {
  __typename?: 'PatientListPaginator';
  data?: Maybe<Array<Patient>>;
  paginator_info: AppPaginator;
};

export type PdfSetting = {
  __typename?: 'PdfSetting';
  establishment?: Maybe<Establishment>;
  establishment_id: Scalars['Int']['output'];
  footer_center?: Maybe<Scalars['String']['output']>;
  footer_left?: Maybe<Scalars['String']['output']>;
  footer_right?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  leftSignature?: Maybe<AppImage>;
  logo?: Maybe<AppImage>;
  rightSignature?: Maybe<AppImage>;
  /** address and other details for pdf */
  subtitle: Scalars['String']['output'];
  /** usually the lab name */
  title: Scalars['String']['output'];
};

/** Indicates what fields are available at the top level of a query operation. */
export type Query = {
  __typename?: 'Query';
  establishmentById?: Maybe<Establishment>;
  getEstablishmentDoctorList: EstablishmentDoctorListPaginator;
  getEstablishmentList: GetEstablishmentListPaginator;
  getMe?: Maybe<User>;
  getPatientList: PatientListPaginator;
  getPdfSetting?: Maybe<PdfSetting>;
  getRecentReports?: Maybe<Array<TestReport>>;
  getReportById?: Maybe<TestReport>;
  getStats: StatsResponse;
  getTemplateBySlug: Template;
  getTemplates: GetTemplatePaginator;
  getTestReports: GetTestReportPaginator;
  getWeeklyLineChartData?: Maybe<Array<WeeklyChartData>>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryEstablishmentByIdArgs = {
  id: Scalars['Int']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetEstablishmentDoctorListArgs = {
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetEstablishmentListArgs = {
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPatientListArgs = {
  first: Scalars['Int']['input'];
  keyword?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPdfSettingArgs = {
  establishment_id?: InputMaybe<Scalars['String']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetRecentReportsArgs = {
  first: Scalars['Int']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetReportByIdArgs = {
  id: Scalars['Int']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetStatsArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTemplateBySlugArgs = {
  slug: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTemplatesArgs = {
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetTestReportsArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  patient_info?: InputMaybe<Scalars['String']['input']>;
  report_name?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};

export type Section = {
  __typename?: 'Section';
  fields?: Maybe<Array<Field>>;
  name: Scalars['String']['output'];
  name_value?: Maybe<Scalars['String']['output']>;
};

export type SectionInput = {
  fields: Array<FieldInput>;
  id?: InputMaybe<Scalars['String']['input']>;
  /** section name */
  name: Scalars['String']['input'];
  name_value?: InputMaybe<Scalars['String']['input']>;
};

/** Directions for ordering a list of records. */
export enum SortOrder {
  /** Sort records in ascending order. */
  Asc = 'ASC',
  /** Sort records in descending order. */
  Desc = 'DESC'
}

export type StatsResponse = {
  __typename?: 'StatsResponse';
  total_patient: Scalars['Int']['output'];
  total_tests_count: Scalars['Int']['output'];
};

export type SubField = {
  __typename?: 'SubField';
  children?: Maybe<Array<SubField>>;
  /** select | multiselect | richttext | field | sub_field */
  input_type: Scalars['String']['output'];
  input_type_values?: Maybe<Array<Scalars['String']['output']>>;
  name: Scalars['String']['output'];
  name_value?: Maybe<Scalars['String']['output']>;
  /** reference ranges */
  reference?: Maybe<Scalars['String']['output']>;
  reference_active?: Maybe<Scalars['Boolean']['output']>;
  reference_value?: Maybe<Scalars['String']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
  unit_active?: Maybe<Scalars['Boolean']['output']>;
  unit_value?: Maybe<Scalars['String']['output']>;
};

export type SubFieldInput = {
  children?: InputMaybe<Array<SubFieldInput>>;
  id?: InputMaybe<Scalars['String']['input']>;
  input_type: FieldInputType;
  input_type_values?: InputMaybe<Array<Scalars['String']['input']>>;
  name: Scalars['String']['input'];
  name_value?: InputMaybe<Scalars['String']['input']>;
  reference?: InputMaybe<Scalars['String']['input']>;
  reference_active?: InputMaybe<Scalars['Boolean']['input']>;
  reference_value?: InputMaybe<Scalars['String']['input']>;
  unit?: InputMaybe<Scalars['String']['input']>;
  unit_active?: InputMaybe<Scalars['Boolean']['input']>;
  unit_value?: InputMaybe<Scalars['String']['input']>;
};

export type Template = {
  __typename?: 'Template';
  establishment: Establishment;
  establishment_id: Scalars['Int']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  /** default is black */
  report_name_color: Scalars['String']['output'];
  slug: Scalars['String']['output'];
  template_data: TemplateData;
  /** default is black */
  title_color: Scalars['String']['output'];
  user: User;
  /** owner */
  user_id: Scalars['Int']['output'];
};

export type TemplateData = {
  __typename?: 'TemplateData';
  fields?: Maybe<Array<Field>>;
  sections?: Maybe<Array<Section>>;
};

export type TestReport = {
  __typename?: 'TestReport';
  collection_date?: Maybe<Scalars['DateTime']['output']>;
  created_at: Scalars['DateTime']['output'];
  doctor?: Maybe<Doctor>;
  establishment_id: Scalars['Int']['output'];
  id: Scalars['Int']['output'];
  latestWhatsAppReceipt?: Maybe<WhatsAppReceipt>;
  patient: Patient;
  patient_id: Scalars['Int']['output'];
  pdf_path?: Maybe<Scalars['String']['output']>;
  remarks?: Maybe<Scalars['String']['output']>;
  report_generation_date?: Maybe<Scalars['DateTime']['output']>;
  /** staff name */
  staff?: Maybe<Scalars['String']['output']>;
  template: Template;
  template_data: TemplateData;
  test_date?: Maybe<Scalars['DateTime']['output']>;
  updated_at: Scalars['DateTime']['output'];
  user: User;
  user_id: Scalars['Int']['output'];
  whatsappable?: Maybe<Array<Maybe<WhatsAppReceipt>>>;
};

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT'
}

/** Account of a person who utilizes this application. */
export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at: Scalars['DateTime']['output'];
  establishment?: Maybe<Establishment>;
  id: Scalars['Int']['output'];
  role: Scalars['String']['output'];
  /** When the account was last updated. */
  updated_at: Scalars['DateTime']['output'];
  username: Scalars['String']['output'];
};

export type WeeklyChartData = {
  __typename?: 'WeeklyChartData';
  template_name: Scalars['String']['output'];
  total_count: Scalars['Int']['output'];
  weekday: Scalars['String']['output'];
};

export type WhatsAppReceipt = {
  __typename?: 'WhatsAppReceipt';
  /** error if message_status is `failed` */
  error_message?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  /** accepted | read | delivered | sent | failed */
  message_status: Scalars['String']['output'];
};

export type AddDoctorMutationVariables = Exact<{
  name: Scalars['String']['input'];
  designation?: InputMaybe<Scalars['String']['input']>;
}>;


export type AddDoctorMutation = { __typename?: 'Mutation', addDoctor: { __typename?: 'Doctor', id: number, name: string } };

export type AddPatientMutationVariables = Exact<{
  name: Scalars['String']['input'];
  age: Scalars['Int']['input'];
  gender: Scalars['String']['input'];
  blood_group?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  phone_number: Scalars['String']['input'];
}>;


export type AddPatientMutation = { __typename?: 'Mutation', addPatient: { __typename?: 'Patient', id: number, name: string, phone_number: string, age: number, gender: string, blood_group?: string | null, address?: string | null } };

export type CreateOwnerWithEstablishmentMutationVariables = Exact<{
  username: Scalars['String']['input'];
  password: Scalars['String']['input'];
  establishment_name: Scalars['String']['input'];
  establishment_phone_number: Scalars['String']['input'];
  establishment_address?: InputMaybe<Scalars['String']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
}>;


export type CreateOwnerWithEstablishmentMutation = { __typename?: 'Mutation', createOwnerWithEstablishment: { __typename?: 'GenericResponse', message: string } };

export type CreateReportMutationVariables = Exact<{
  template_id: Scalars['Int']['input'];
  sections_input?: InputMaybe<Array<SectionInput> | SectionInput>;
  fields_input?: InputMaybe<Array<FieldInput> | FieldInput>;
  pdf?: InputMaybe<Scalars['Upload']['input']>;
  patient_id: Scalars['Int']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
  staff?: InputMaybe<Scalars['String']['input']>;
  collection_date?: InputMaybe<Scalars['DateTime']['input']>;
  test_date?: InputMaybe<Scalars['DateTime']['input']>;
  report_generation_date?: InputMaybe<Scalars['DateTime']['input']>;
  doctor_id?: InputMaybe<Scalars['Int']['input']>;
}>;


export type CreateReportMutation = { __typename?: 'Mutation', createReport: { __typename?: 'GenericResponse', message: string } };

export type CreateTemplateMutationVariables = Exact<{
  name: Scalars['String']['input'];
  title_color?: InputMaybe<Scalars['String']['input']>;
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput> | SectionInput>;
  fields_input?: InputMaybe<Array<FieldInput> | FieldInput>;
}>;


export type CreateTemplateMutation = { __typename?: 'Mutation', createTemplate: { __typename?: 'GenericResponse', message: string } };

export type DeleteDoctorMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeleteDoctorMutation = { __typename?: 'Mutation', deleteDoctor: { __typename?: 'GenericResponse', message: string } };

export type DeletePatientMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeletePatientMutation = { __typename?: 'Mutation', deletePatient: { __typename?: 'GenericResponse', message: string } };

export type DeleteReportMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeleteReportMutation = { __typename?: 'Mutation', deleteReport: { __typename?: 'GenericResponse', message: string } };

export type DeleteTemplateMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeleteTemplateMutation = { __typename?: 'Mutation', deleteTemplate: { __typename?: 'GenericResponse', message: string } };

export type LoginMutationVariables = Exact<{
  username: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type LoginMutation = { __typename?: 'Mutation', login: { __typename?: 'LoginResponse', token: string, user: { __typename?: 'User', id: number, username: string, role: string } } };

export type LogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type LogoutMutation = { __typename?: 'Mutation', logout: { __typename?: 'GenericResponse', message: string } };

export type SendTestReportMutationVariables = Exact<{
  report_id: Scalars['Int']['input'];
  patient_id: Scalars['Int']['input'];
  whatsapp_number?: InputMaybe<Scalars['String']['input']>;
  pdf: Scalars['Upload']['input'];
}>;


export type SendTestReportMutation = { __typename?: 'Mutation', sendTestReport: { __typename?: 'GenericResponse', message: string } };

export type UpdateDoctorMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  designation?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateDoctorMutation = { __typename?: 'Mutation', updateDoctor: { __typename?: 'GenericResponse', message: string } };

export type UpdateEstablishmentMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  establishment_name?: InputMaybe<Scalars['String']['input']>;
  establishment_phone_number?: InputMaybe<Scalars['String']['input']>;
  establishment_address?: InputMaybe<Scalars['String']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
}>;


export type UpdateEstablishmentMutation = { __typename?: 'Mutation', updateEstablishment: { __typename?: 'GenericResponse', message: string } };

export type UpdateMyEstablishmentMutationVariables = Exact<{
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
  wa_phone_number_id?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateMyEstablishmentMutation = { __typename?: 'Mutation', updateMyEstablishment: { __typename?: 'GenericResponse', message: string } };

export type UpdateMyProfileMutationVariables = Exact<{
  username?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateMyProfileMutation = { __typename?: 'Mutation', updateMyProfile: { __typename?: 'GenericResponse', message: string } };

export type UpdateOwnerMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  username: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type UpdateOwnerMutation = { __typename?: 'Mutation', updateOwner: { __typename?: 'GenericResponse', message: string } };

export type UpdatePatientMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  age?: InputMaybe<Scalars['Int']['input']>;
  gender?: InputMaybe<Scalars['String']['input']>;
  blood_group?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdatePatientMutation = { __typename?: 'Mutation', updatePatient: { __typename?: 'GenericResponse', message: string } };

export type UpdateReportMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
  staff?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput> | SectionInput>;
  fields_input?: InputMaybe<Array<FieldInput> | FieldInput>;
  template_id?: InputMaybe<Scalars['Int']['input']>;
  patient_id?: InputMaybe<Scalars['Int']['input']>;
  collection_date?: InputMaybe<Scalars['DateTime']['input']>;
  test_date?: InputMaybe<Scalars['DateTime']['input']>;
  report_generation_date?: InputMaybe<Scalars['DateTime']['input']>;
  doctor_id?: InputMaybe<Scalars['Int']['input']>;
}>;


export type UpdateReportMutation = { __typename?: 'Mutation', updateReport: { __typename?: 'GenericResponse', message: string } };

export type UpdateTemplateMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  title_color?: InputMaybe<Scalars['String']['input']>;
  report_name_color?: InputMaybe<Scalars['String']['input']>;
  sections_input?: InputMaybe<Array<SectionInput> | SectionInput>;
  fields_input?: InputMaybe<Array<FieldInput> | FieldInput>;
}>;


export type UpdateTemplateMutation = { __typename?: 'Mutation', updateTemplate: { __typename?: 'GenericResponse', message: string } };

export type UpsertPdfSettingMutationVariables = Exact<{
  establishment_id?: InputMaybe<Scalars['Int']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  subtitle?: InputMaybe<Scalars['String']['input']>;
  footer_left?: InputMaybe<Scalars['String']['input']>;
  footer_right?: InputMaybe<Scalars['String']['input']>;
  footer_center?: InputMaybe<Scalars['String']['input']>;
  logo?: InputMaybe<Scalars['Upload']['input']>;
  left_signature?: InputMaybe<Scalars['Upload']['input']>;
  right_signature?: InputMaybe<Scalars['Upload']['input']>;
}>;


export type UpsertPdfSettingMutation = { __typename?: 'Mutation', upsertPdfSetting: { __typename?: 'GenericResponse', message: string } };

export type GetEstablishmentDoctorListQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetEstablishmentDoctorListQuery = { __typename?: 'Query', getEstablishmentDoctorList: { __typename?: 'EstablishmentDoctorListPaginator', data?: Array<{ __typename?: 'Doctor', id: number, name: string, designation?: string | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetEstablishmentListQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetEstablishmentListQuery = { __typename?: 'Query', getEstablishmentList: { __typename?: 'GetEstablishmentListPaginator', data?: Array<{ __typename?: 'Establishment', id: number, name: string, address: string, phone_number: string, owner: { __typename?: 'User', id: number, username: string } }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetMeQueryVariables = Exact<{ [key: string]: never; }>;


export type GetMeQuery = { __typename?: 'Query', getMe?: { __typename?: 'User', id: number, username: string, establishment?: { __typename?: 'Establishment', id: number, name: string, address: string, phone_number: string, logo?: { __typename?: 'AppImage', path: string } | null } | null } | null };

export type GetPatientListQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetPatientListQuery = { __typename?: 'Query', getPatientList: { __typename?: 'PatientListPaginator', data?: Array<{ __typename?: 'Patient', id: number, name: string, age: number, gender: string, blood_group?: string | null, address?: string | null, phone_number: string }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetPdfSettingQueryVariables = Exact<{ [key: string]: never; }>;


export type GetPdfSettingQuery = { __typename?: 'Query', getPdfSetting?: { __typename?: 'PdfSetting', id: number, establishment_id: number, title: string, subtitle: string, footer_left?: string | null, footer_right?: string | null, footer_center?: string | null, logo?: { __typename?: 'AppImage', id: number, path: string, hash: string, order: number } | null, leftSignature?: { __typename?: 'AppImage', id: number, path: string, hash: string } | null, rightSignature?: { __typename?: 'AppImage', id: number, path: string, hash: string } | null } | null };

export type GetRecentReportsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
}>;


export type GetRecentReportsQuery = { __typename?: 'Query', getRecentReports?: Array<{ __typename?: 'TestReport', id: number, patient: { __typename?: 'Patient', id: number, name: string }, template: { __typename?: 'Template', id: number, name: string } }> | null };

export type GetReportByIdQueryVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type GetReportByIdQuery = { __typename?: 'Query', getReportById?: { __typename?: 'TestReport', id: number, remarks?: string | null, staff?: string | null, collection_date?: any | null, test_date?: any | null, report_generation_date?: any | null, patient: { __typename?: 'Patient', id: number, name: string, phone_number: string, age: number, gender: string, address?: string | null, blood_group?: string | null }, template_data: { __typename?: 'TemplateData', fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, reference?: string | null, reference_value?: string | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null }> | null }> | null, sections?: Array<{ __typename?: 'Section', name: string, name_value?: string | null, fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_active?: boolean | null, reference_value?: string | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null }> | null }> | null }> | null }, template: { __typename?: 'Template', id: number, name: string, title_color: string, report_name_color: string, template_data: { __typename?: 'TemplateData', fields?: Array<{ __typename?: 'Field', name: string, unit?: string | null, reference?: string | null, input_type?: string | null }> | null, sections?: Array<{ __typename?: 'Section', name: string, name_value?: string | null, fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_active?: boolean | null, reference_value?: string | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null }> | null }> | null }> | null } }, doctor?: { __typename?: 'Doctor', id: number, name: string } | null } | null };

export type GetStatsQueryVariables = Exact<{
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetStatsQuery = { __typename?: 'Query', getStats: { __typename?: 'StatsResponse', total_patient: number, total_tests_count: number } };

export type GetTemplateBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type GetTemplateBySlugQuery = { __typename?: 'Query', getTemplateBySlug: { __typename?: 'Template', id: number, name: string, title_color: string, report_name_color: string, template_data: { __typename?: 'TemplateData', fields?: Array<{ __typename?: 'Field', name: string, unit?: string | null, reference?: string | null, input_type?: string | null }> | null, sections?: Array<{ __typename?: 'Section', name: string, fields?: Array<{ __typename?: 'Field', name: string, unit?: string | null, unit_active?: boolean | null, reference?: string | null, reference_active?: boolean | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, input_type: string, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null }> | null }> | null }> | null } } };

export type GetTemplatesQueryVariables = Exact<{
  name?: InputMaybe<Scalars['String']['input']>;
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetTemplatesQuery = { __typename?: 'Query', getTemplates: { __typename?: 'GetTemplatePaginator', data?: Array<{ __typename?: 'Template', id: number, name: string, slug: string }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetTestReportsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  report_name?: InputMaybe<Scalars['String']['input']>;
  patient_info?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetTestReportsQuery = { __typename?: 'Query', getTestReports: { __typename?: 'GetTestReportPaginator', data?: Array<{ __typename?: 'TestReport', id: number, created_at: any, remarks?: string | null, staff?: string | null, collection_date?: any | null, report_generation_date?: any | null, test_date?: any | null, patient: { __typename?: 'Patient', id: number, name: string, phone_number: string, address?: string | null, age: number, gender: string, blood_group?: string | null }, template: { __typename?: 'Template', id: number, name: string, slug: string, title_color: string, report_name_color: string }, template_data: { __typename?: 'TemplateData', fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, reference?: string | null, reference_value?: string | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null }> | null }> | null, sections?: Array<{ __typename?: 'Section', name: string, name_value?: string | null, fields?: Array<{ __typename?: 'Field', name: string, name_value?: string | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null, input_type?: string | null, input_type_values?: Array<string> | null, sub_fields?: Array<{ __typename?: 'SubField', name: string, name_value?: string | null, input_type: string, input_type_values?: Array<string> | null, unit?: string | null, unit_value?: string | null, unit_active?: boolean | null, reference?: string | null, reference_value?: string | null, reference_active?: boolean | null }> | null }> | null }> | null }, doctor?: { __typename?: 'Doctor', id: number, name: string } | null, latestWhatsAppReceipt?: { __typename?: 'WhatsAppReceipt', message_status: string, error_message?: string | null } | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number } } };

export type GetWeeklyLineChartDataQueryVariables = Exact<{ [key: string]: never; }>;


export type GetWeeklyLineChartDataQuery = { __typename?: 'Query', getWeeklyLineChartData?: Array<{ __typename?: 'WeeklyChartData', template_name: string, total_count: number, weekday: string }> | null };
