/* eslint-disable */
import * as types from './graphql';
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  mutation AddDoctor($name: String!, $designation: String) {\n    addDoctor(name: $name, designation: $designation) {\n      id\n      name\n    }\n  }\n": typeof types.AddDoctorDocument,
    "\n  mutation AddPatient(\n    $name: String!\n    $age: Int!\n    $gender: String!\n    $blood_group: String\n    $address: String,\n    $phone_number: String!\n  ) {\n    addPatient(\n      name: $name\n      age: $age\n      gender: $gender\n      blood_group: $blood_group\n      address: $address\n      phone_number: $phone_number\n    ) {\n      id\n      name\n      phone_number\n      age\n      gender\n      blood_group\n      address\n    }\n  }\n": typeof types.AddPatientDocument,
    "\n  mutation CreateOwnerWithEstablishment(\n    $username: String!\n    $password: String!\n    $establishment_name: String!\n    $establishment_phone_number: String!\n    $establishment_address: String\n    $wa_phone_number_id: String\n    $logo: Upload\n  ) {\n    createOwnerWithEstablishment(\n      username: $username\n      password: $password\n      establishment_name: $establishment_name\n      establishment_phone_number: $establishment_phone_number\n      establishment_address: $establishment_address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n": typeof types.CreateOwnerWithEstablishmentDocument,
    "\n  mutation CreateReport(\n    $template_id: Int!\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n    $pdf: Upload\n    $patient_id: Int!\n    $remarks: String\n    $staff: String\n    $collection_date: DateTime\n    $test_date: DateTime\n    $report_generation_date: DateTime\n    $doctor_id: Int\n  ) {\n    createReport(\n      template_id: $template_id\n      sections_input: $sections_input\n      fields_input: $fields_input\n      pdf: $pdf\n      patient_id: $patient_id\n      remarks: $remarks\n      staff: $staff\n      collection_date: $collection_date\n      test_date: $test_date\n      report_generation_date: $report_generation_date\n      doctor_id: $doctor_id\n    ) {\n      message\n    }\n  }\n": typeof types.CreateReportDocument,
    "\n  mutation CreateTemplate(\n    $name: String!\n    $title_color: String\n    $report_name_color: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n  ) {\n    createTemplate(\n      name: $name\n      title_color: $title_color\n      report_name_color: $report_name_color\n      sections_input: $sections_input\n      fields_input: $fields_input\n    ) {\n      message\n    }\n  }\n": typeof types.CreateTemplateDocument,
    "\n  mutation DeleteDoctor($id: Int!) {\n    deleteDoctor(id: $id) {\n      message\n    }\n  }\n": typeof types.DeleteDoctorDocument,
    "\n  mutation DeletePatient($id: Int!) {\n    deletePatient(id: $id) {\n      message\n    }\n  }\n": typeof types.DeletePatientDocument,
    "\n  mutation DeleteReport($id: Int!) {\n    deleteReport(id: $id) {\n      message\n    }\n  }                                   \n": typeof types.DeleteReportDocument,
    "\n  mutation DeleteTemplate($id: Int!) {\n    deleteTemplate(id: $id) {\n      message\n    }\n  }\n": typeof types.DeleteTemplateDocument,
    "\n  mutation Login(\n    $username: String!\n    $password: String!\n  ) {\n    login(username: $username, password: $password) {\n      token\n      user {\n        id\n        username\n        role\n      }\n    }\n  }\n": typeof types.LoginDocument,
    "\n  mutation Logout {\n    logout {\n      message\n    }\n  }\n": typeof types.LogoutDocument,
    "\n  mutation SendTestReport(\n    $report_id: Int!\n    $patient_id: Int!\n    $whatsapp_number: String\n    $pdf: Upload!\n  ) {\n    sendTestReport(\n      report_id: $report_id\n      patient_id: $patient_id\n      whatsapp_number: $whatsapp_number\n      pdf: $pdf\n    ) {\n      message\n    }\n  }\n": typeof types.SendTestReportDocument,
    "\n  mutation UpdateDoctor($id: Int!, $name: String, $designation: String) {\n    updateDoctor(id: $id, name: $name, designation: $designation) {\n      message\n    }\n  }\n": typeof types.UpdateDoctorDocument,
    "\n  mutation UpdateEstablishment(\n    $id: Int!\n    $establishment_name: String\n    $establishment_phone_number: String\n    $establishment_address: String\n    $wa_phone_number_id: String\n    $logo: Upload\n  ) {\n    updateEstablishment(\n      id: $id\n      establishment_name: $establishment_name\n      establishment_phone_number: $establishment_phone_number\n      establishment_address: $establishment_address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n": typeof types.UpdateEstablishmentDocument,
    "\n  mutation UpdateMyEstablishment(\n    $name: String\n    $phone_number: String\n    $address: String\n    $logo: Upload\n    $wa_phone_number_id: String\n  ) {\n    updateMyEstablishment(\n      name: $name\n      phone_number: $phone_number\n      address: $address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n": typeof types.UpdateMyEstablishmentDocument,
    "\n  mutation UpdateMyProfile($username: String, $password: String) {\n    updateMyProfile(username: $username, password: $password) {\n      message\n    }\n  }\n": typeof types.UpdateMyProfileDocument,
    "\n  mutation UpdateOwner(\n    $id: Int!\n    $username: String!\n    $password: String!\n  ) {\n    updateOwner(\n      id: $id\n      username: $username\n      password: $password\n    ) {\n      message\n    }\n  }\n": typeof types.UpdateOwnerDocument,
    "\n  mutation UpdatePatient(\n    $id: Int!\n    $name: String\n    $age: Int\n    $gender: String\n    $blood_group: String\n    $address: String,\n    $phone_number: String\n  ) {\n    updatePatient(\n      id: $id\n      name: $name\n      age: $age\n      gender: $gender\n      blood_group: $blood_group\n      address: $address\n      phone_number: $phone_number\n    ) {\n      message\n    }\n  }\n": typeof types.UpdatePatientDocument,
    "\n  mutation UpdateReport(\n    $id: Int!\n    $remarks: String\n    $staff: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n    $template_id: Int\n    $patient_id: Int\n    $collection_date: DateTime\n    $test_date: DateTime\n    $report_generation_date: DateTime\n    $doctor_id: Int\n  ) {\n    updateReport(\n      id: $id\n      remarks: $remarks\n      staff: $staff\n      sections_input: $sections_input\n      fields_input: $fields_input\n      template_id: $template_id\n      patient_id: $patient_id\n      collection_date: $collection_date\n      test_date: $test_date\n      report_generation_date: $report_generation_date\n      doctor_id: $doctor_id\n    ) {\n      message\n    }\n  }": typeof types.UpdateReportDocument,
    "\n  mutation UpdateTemplate(\n    $id: Int!\n    $name: String\n    $title_color: String\n    $report_name_color: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n  ) {\n    updateTemplate(\n      id: $id\n      name: $name\n      title_color: $title_color\n      report_name_color: $report_name_color\n      sections_input: $sections_input\n      fields_input: $fields_input\n    ) {\n      message\n    }\n  }\n": typeof types.UpdateTemplateDocument,
    "\n  mutation UpsertPdfSetting(\n    $establishment_id: Int\n    $title: String\n    $subtitle: String\n    $footer_left: String\n    $footer_right: String\n    $footer_center: String\n    $logo: Upload\n    $left_signature: Upload\n    $right_signature: Upload\n  ) {\n    upsertPdfSetting(\n      establishment_id: $establishment_id\n      title: $title\n      subtitle: $subtitle\n      footer_left: $footer_left\n      footer_right: $footer_right\n      footer_center: $footer_center\n      logo: $logo\n      left_signature: $left_signature\n      right_signature: $right_signature\n    ) {\n      message\n    }\n  }\n": typeof types.UpsertPdfSettingDocument,
    "\n  query GetEstablishmentDoctorList($first: Int!, $name: String, $page: Int) {\n    getEstablishmentDoctorList(\n      first: $first\n      name: $name\n      page: $page\n    ) {\n      data {\n        id\n        name\n        designation\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n": typeof types.GetEstablishmentDoctorListDocument,
    "\n  query GetEstablishmentList($first: Int!, $page: Int, $name: String) {\n    getEstablishmentList(\n      first: $first\n      page: $page\n      name: $name\n    ) {\n      data {\n        id\n        name\n        address\n        phone_number\n        owner {\n          id\n          username\n        }\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n": typeof types.GetEstablishmentListDocument,
    "\n  query GetMe {\n    getMe {\n      id\n      username\n      establishment {\n        id\n        name\n        address\n        phone_number\n        logo {\n          path\n        }\n      }\n    }\n  }\n": typeof types.GetMeDocument,
    "\n  query GetPatientList($first: Int!, $page: Int, $keyword: String) {\n    getPatientList(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        name\n        age\n        gender\n        blood_group\n        address\n        phone_number\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n": typeof types.GetPatientListDocument,
    "\n  query GetPdfSetting {\n    getPdfSetting {\n      id\n      establishment_id\n      title\n      subtitle\n      footer_left\n      footer_right\n      footer_center\n      logo {\n        id\n        path\n        hash\n        order\n      }\n      leftSignature {\n        id\n        path\n        hash\n      }\n      rightSignature {\n        id\n        path\n        hash\n      }\n    }\n  }\n": typeof types.GetPdfSettingDocument,
    "\n  query GetRecentReports($first: Int!) {\n    getRecentReports(first: $first) {\n      id\n      patient {\n        id\n        name\n      }\n      template {\n        id\n        name\n      }\n    }\n  }\n": typeof types.GetRecentReportsDocument,
    "\n  query GetReportById($id: Int!) {\n    getReportById(id: $id) {\n      id\n      remarks\n      staff\n      collection_date\n      test_date\n      report_generation_date\n      patient {\n        id\n        name\n        phone_number\n        age\n        gender\n        address\n        blood_group\n      }\n      template_data {\n        fields {\n          name\n          name_value\n          unit\n          unit_value\n          reference\n          reference_value\n          input_type\n          input_type_values\n          sub_fields {\n            name\n            name_value\n            input_type\n            input_type_values\n          }\n        }\n        sections {\n          name\n          name_value\n          fields {\n            name\n            name_value\n            unit\n            unit_value\n            unit_active\n            reference\n            reference_active\n            reference_value\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              name_value\n              input_type\n              input_type_values\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n            }\n          }\n        }\n      }\n      template {\n        id\n        name\n        title_color\n        report_name_color\n        template_data {\n          fields {\n            name\n            unit\n            reference\n            input_type\n          }\n          sections {\n            name\n            name_value\n            fields {\n              name\n              name_value\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_active\n              reference_value\n              input_type\n              input_type_values\n              sub_fields {\n                name\n                name_value\n                input_type\n                input_type_values\n                unit\n                unit_value\n                unit_active\n                reference\n                reference_value\n                reference_active\n              }\n            }\n          }\n        }\n      }\n      doctor {\n        id\n        name\n      }\n    }\n  }\n": typeof types.GetReportByIdDocument,
    "\n  query GetStats($start_date: DateTime, $end_date: DateTime) {\n    getStats(start_date: $start_date, end_date: $end_date) {\n      total_patient\n      total_tests_count\n    }\n  }\n": typeof types.GetStatsDocument,
    "\n  query GetTemplateBySlug($slug: String!) {\n    getTemplateBySlug(slug: $slug) {\n      id\n      name\n      title_color\n      report_name_color\n      template_data {\n        fields {\n          name\n          unit\n          reference\n          input_type\n        }\n        sections {\n          name\n          fields {\n            name\n            unit\n            unit_active\n            reference\n            reference_active\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              input_type\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n            }\n          }\n        }\n      }\n    }\n  }\n": typeof types.GetTemplateBySlugDocument,
    "\n  query GetTemplates(\n    $name: String\n    $first: Int!\n    $page: Int\n  ) {\n    getTemplates(\n      name: $name\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        name\n        slug\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n  ": typeof types.GetTemplatesDocument,
    "\n  query GetTestReports(\n    $first: Int!\n    $page: Int\n    $start_date: DateTime\n    $end_date: DateTime\n    $report_name: String\n    $patient_info: String\n  ) {\n    getTestReports(\n      first: $first\n      page: $page\n      start_date: $start_date\n      end_date: $end_date\n      report_name: $report_name\n      patient_info: $patient_info\n    ) {\n      data {\n        id\n        patient {\n          id\n          name\n          phone_number\n          address\n          age\n          gender\n          blood_group\n        }\n        template {\n          id\n          name\n          slug\n          title_color\n          report_name_color\n        }\n        template_data {\n          fields {\n            name\n            name_value\n            unit\n            unit_value\n            reference\n            reference_value\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              name_value\n              input_type\n              input_type_values\n            }\n          }\n          sections {\n            name\n            name_value\n            fields {\n              name\n              name_value\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n              input_type\n              input_type_values\n              sub_fields {\n                name\n                name_value\n                input_type\n                input_type_values\n                unit\n                unit_value\n                unit_active\n                reference\n                reference_value\n                reference_active\n              }\n            }\n          }\n        }\n        doctor {\n          id\n          name\n        }\n        latestWhatsAppReceipt {\n          message_status\n          error_message\n        }\n        created_at\n        remarks\n        staff\n        collection_date\n        report_generation_date\n        test_date\n      }\n      paginator_info {\n        total\n      }\n    }\n  }                                      \n": typeof types.GetTestReportsDocument,
    "\n  query GetWeeklyLineChartData {\n    getWeeklyLineChartData {\n      template_name\n      total_count\n      weekday\n    }\n  }\n": typeof types.GetWeeklyLineChartDataDocument,
};
const documents: Documents = {
    "\n  mutation AddDoctor($name: String!, $designation: String) {\n    addDoctor(name: $name, designation: $designation) {\n      id\n      name\n    }\n  }\n": types.AddDoctorDocument,
    "\n  mutation AddPatient(\n    $name: String!\n    $age: Int!\n    $gender: String!\n    $blood_group: String\n    $address: String,\n    $phone_number: String!\n  ) {\n    addPatient(\n      name: $name\n      age: $age\n      gender: $gender\n      blood_group: $blood_group\n      address: $address\n      phone_number: $phone_number\n    ) {\n      id\n      name\n      phone_number\n      age\n      gender\n      blood_group\n      address\n    }\n  }\n": types.AddPatientDocument,
    "\n  mutation CreateOwnerWithEstablishment(\n    $username: String!\n    $password: String!\n    $establishment_name: String!\n    $establishment_phone_number: String!\n    $establishment_address: String\n    $wa_phone_number_id: String\n    $logo: Upload\n  ) {\n    createOwnerWithEstablishment(\n      username: $username\n      password: $password\n      establishment_name: $establishment_name\n      establishment_phone_number: $establishment_phone_number\n      establishment_address: $establishment_address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n": types.CreateOwnerWithEstablishmentDocument,
    "\n  mutation CreateReport(\n    $template_id: Int!\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n    $pdf: Upload\n    $patient_id: Int!\n    $remarks: String\n    $staff: String\n    $collection_date: DateTime\n    $test_date: DateTime\n    $report_generation_date: DateTime\n    $doctor_id: Int\n  ) {\n    createReport(\n      template_id: $template_id\n      sections_input: $sections_input\n      fields_input: $fields_input\n      pdf: $pdf\n      patient_id: $patient_id\n      remarks: $remarks\n      staff: $staff\n      collection_date: $collection_date\n      test_date: $test_date\n      report_generation_date: $report_generation_date\n      doctor_id: $doctor_id\n    ) {\n      message\n    }\n  }\n": types.CreateReportDocument,
    "\n  mutation CreateTemplate(\n    $name: String!\n    $title_color: String\n    $report_name_color: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n  ) {\n    createTemplate(\n      name: $name\n      title_color: $title_color\n      report_name_color: $report_name_color\n      sections_input: $sections_input\n      fields_input: $fields_input\n    ) {\n      message\n    }\n  }\n": types.CreateTemplateDocument,
    "\n  mutation DeleteDoctor($id: Int!) {\n    deleteDoctor(id: $id) {\n      message\n    }\n  }\n": types.DeleteDoctorDocument,
    "\n  mutation DeletePatient($id: Int!) {\n    deletePatient(id: $id) {\n      message\n    }\n  }\n": types.DeletePatientDocument,
    "\n  mutation DeleteReport($id: Int!) {\n    deleteReport(id: $id) {\n      message\n    }\n  }                                   \n": types.DeleteReportDocument,
    "\n  mutation DeleteTemplate($id: Int!) {\n    deleteTemplate(id: $id) {\n      message\n    }\n  }\n": types.DeleteTemplateDocument,
    "\n  mutation Login(\n    $username: String!\n    $password: String!\n  ) {\n    login(username: $username, password: $password) {\n      token\n      user {\n        id\n        username\n        role\n      }\n    }\n  }\n": types.LoginDocument,
    "\n  mutation Logout {\n    logout {\n      message\n    }\n  }\n": types.LogoutDocument,
    "\n  mutation SendTestReport(\n    $report_id: Int!\n    $patient_id: Int!\n    $whatsapp_number: String\n    $pdf: Upload!\n  ) {\n    sendTestReport(\n      report_id: $report_id\n      patient_id: $patient_id\n      whatsapp_number: $whatsapp_number\n      pdf: $pdf\n    ) {\n      message\n    }\n  }\n": types.SendTestReportDocument,
    "\n  mutation UpdateDoctor($id: Int!, $name: String, $designation: String) {\n    updateDoctor(id: $id, name: $name, designation: $designation) {\n      message\n    }\n  }\n": types.UpdateDoctorDocument,
    "\n  mutation UpdateEstablishment(\n    $id: Int!\n    $establishment_name: String\n    $establishment_phone_number: String\n    $establishment_address: String\n    $wa_phone_number_id: String\n    $logo: Upload\n  ) {\n    updateEstablishment(\n      id: $id\n      establishment_name: $establishment_name\n      establishment_phone_number: $establishment_phone_number\n      establishment_address: $establishment_address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n": types.UpdateEstablishmentDocument,
    "\n  mutation UpdateMyEstablishment(\n    $name: String\n    $phone_number: String\n    $address: String\n    $logo: Upload\n    $wa_phone_number_id: String\n  ) {\n    updateMyEstablishment(\n      name: $name\n      phone_number: $phone_number\n      address: $address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n": types.UpdateMyEstablishmentDocument,
    "\n  mutation UpdateMyProfile($username: String, $password: String) {\n    updateMyProfile(username: $username, password: $password) {\n      message\n    }\n  }\n": types.UpdateMyProfileDocument,
    "\n  mutation UpdateOwner(\n    $id: Int!\n    $username: String!\n    $password: String!\n  ) {\n    updateOwner(\n      id: $id\n      username: $username\n      password: $password\n    ) {\n      message\n    }\n  }\n": types.UpdateOwnerDocument,
    "\n  mutation UpdatePatient(\n    $id: Int!\n    $name: String\n    $age: Int\n    $gender: String\n    $blood_group: String\n    $address: String,\n    $phone_number: String\n  ) {\n    updatePatient(\n      id: $id\n      name: $name\n      age: $age\n      gender: $gender\n      blood_group: $blood_group\n      address: $address\n      phone_number: $phone_number\n    ) {\n      message\n    }\n  }\n": types.UpdatePatientDocument,
    "\n  mutation UpdateReport(\n    $id: Int!\n    $remarks: String\n    $staff: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n    $template_id: Int\n    $patient_id: Int\n    $collection_date: DateTime\n    $test_date: DateTime\n    $report_generation_date: DateTime\n    $doctor_id: Int\n  ) {\n    updateReport(\n      id: $id\n      remarks: $remarks\n      staff: $staff\n      sections_input: $sections_input\n      fields_input: $fields_input\n      template_id: $template_id\n      patient_id: $patient_id\n      collection_date: $collection_date\n      test_date: $test_date\n      report_generation_date: $report_generation_date\n      doctor_id: $doctor_id\n    ) {\n      message\n    }\n  }": types.UpdateReportDocument,
    "\n  mutation UpdateTemplate(\n    $id: Int!\n    $name: String\n    $title_color: String\n    $report_name_color: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n  ) {\n    updateTemplate(\n      id: $id\n      name: $name\n      title_color: $title_color\n      report_name_color: $report_name_color\n      sections_input: $sections_input\n      fields_input: $fields_input\n    ) {\n      message\n    }\n  }\n": types.UpdateTemplateDocument,
    "\n  mutation UpsertPdfSetting(\n    $establishment_id: Int\n    $title: String\n    $subtitle: String\n    $footer_left: String\n    $footer_right: String\n    $footer_center: String\n    $logo: Upload\n    $left_signature: Upload\n    $right_signature: Upload\n  ) {\n    upsertPdfSetting(\n      establishment_id: $establishment_id\n      title: $title\n      subtitle: $subtitle\n      footer_left: $footer_left\n      footer_right: $footer_right\n      footer_center: $footer_center\n      logo: $logo\n      left_signature: $left_signature\n      right_signature: $right_signature\n    ) {\n      message\n    }\n  }\n": types.UpsertPdfSettingDocument,
    "\n  query GetEstablishmentDoctorList($first: Int!, $name: String, $page: Int) {\n    getEstablishmentDoctorList(\n      first: $first\n      name: $name\n      page: $page\n    ) {\n      data {\n        id\n        name\n        designation\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n": types.GetEstablishmentDoctorListDocument,
    "\n  query GetEstablishmentList($first: Int!, $page: Int, $name: String) {\n    getEstablishmentList(\n      first: $first\n      page: $page\n      name: $name\n    ) {\n      data {\n        id\n        name\n        address\n        phone_number\n        owner {\n          id\n          username\n        }\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n": types.GetEstablishmentListDocument,
    "\n  query GetMe {\n    getMe {\n      id\n      username\n      establishment {\n        id\n        name\n        address\n        phone_number\n        logo {\n          path\n        }\n      }\n    }\n  }\n": types.GetMeDocument,
    "\n  query GetPatientList($first: Int!, $page: Int, $keyword: String) {\n    getPatientList(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        name\n        age\n        gender\n        blood_group\n        address\n        phone_number\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n": types.GetPatientListDocument,
    "\n  query GetPdfSetting {\n    getPdfSetting {\n      id\n      establishment_id\n      title\n      subtitle\n      footer_left\n      footer_right\n      footer_center\n      logo {\n        id\n        path\n        hash\n        order\n      }\n      leftSignature {\n        id\n        path\n        hash\n      }\n      rightSignature {\n        id\n        path\n        hash\n      }\n    }\n  }\n": types.GetPdfSettingDocument,
    "\n  query GetRecentReports($first: Int!) {\n    getRecentReports(first: $first) {\n      id\n      patient {\n        id\n        name\n      }\n      template {\n        id\n        name\n      }\n    }\n  }\n": types.GetRecentReportsDocument,
    "\n  query GetReportById($id: Int!) {\n    getReportById(id: $id) {\n      id\n      remarks\n      staff\n      collection_date\n      test_date\n      report_generation_date\n      patient {\n        id\n        name\n        phone_number\n        age\n        gender\n        address\n        blood_group\n      }\n      template_data {\n        fields {\n          name\n          name_value\n          unit\n          unit_value\n          reference\n          reference_value\n          input_type\n          input_type_values\n          sub_fields {\n            name\n            name_value\n            input_type\n            input_type_values\n          }\n        }\n        sections {\n          name\n          name_value\n          fields {\n            name\n            name_value\n            unit\n            unit_value\n            unit_active\n            reference\n            reference_active\n            reference_value\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              name_value\n              input_type\n              input_type_values\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n            }\n          }\n        }\n      }\n      template {\n        id\n        name\n        title_color\n        report_name_color\n        template_data {\n          fields {\n            name\n            unit\n            reference\n            input_type\n          }\n          sections {\n            name\n            name_value\n            fields {\n              name\n              name_value\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_active\n              reference_value\n              input_type\n              input_type_values\n              sub_fields {\n                name\n                name_value\n                input_type\n                input_type_values\n                unit\n                unit_value\n                unit_active\n                reference\n                reference_value\n                reference_active\n              }\n            }\n          }\n        }\n      }\n      doctor {\n        id\n        name\n      }\n    }\n  }\n": types.GetReportByIdDocument,
    "\n  query GetStats($start_date: DateTime, $end_date: DateTime) {\n    getStats(start_date: $start_date, end_date: $end_date) {\n      total_patient\n      total_tests_count\n    }\n  }\n": types.GetStatsDocument,
    "\n  query GetTemplateBySlug($slug: String!) {\n    getTemplateBySlug(slug: $slug) {\n      id\n      name\n      title_color\n      report_name_color\n      template_data {\n        fields {\n          name\n          unit\n          reference\n          input_type\n        }\n        sections {\n          name\n          fields {\n            name\n            unit\n            unit_active\n            reference\n            reference_active\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              input_type\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n            }\n          }\n        }\n      }\n    }\n  }\n": types.GetTemplateBySlugDocument,
    "\n  query GetTemplates(\n    $name: String\n    $first: Int!\n    $page: Int\n  ) {\n    getTemplates(\n      name: $name\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        name\n        slug\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n  ": types.GetTemplatesDocument,
    "\n  query GetTestReports(\n    $first: Int!\n    $page: Int\n    $start_date: DateTime\n    $end_date: DateTime\n    $report_name: String\n    $patient_info: String\n  ) {\n    getTestReports(\n      first: $first\n      page: $page\n      start_date: $start_date\n      end_date: $end_date\n      report_name: $report_name\n      patient_info: $patient_info\n    ) {\n      data {\n        id\n        patient {\n          id\n          name\n          phone_number\n          address\n          age\n          gender\n          blood_group\n        }\n        template {\n          id\n          name\n          slug\n          title_color\n          report_name_color\n        }\n        template_data {\n          fields {\n            name\n            name_value\n            unit\n            unit_value\n            reference\n            reference_value\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              name_value\n              input_type\n              input_type_values\n            }\n          }\n          sections {\n            name\n            name_value\n            fields {\n              name\n              name_value\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n              input_type\n              input_type_values\n              sub_fields {\n                name\n                name_value\n                input_type\n                input_type_values\n                unit\n                unit_value\n                unit_active\n                reference\n                reference_value\n                reference_active\n              }\n            }\n          }\n        }\n        doctor {\n          id\n          name\n        }\n        latestWhatsAppReceipt {\n          message_status\n          error_message\n        }\n        created_at\n        remarks\n        staff\n        collection_date\n        report_generation_date\n        test_date\n      }\n      paginator_info {\n        total\n      }\n    }\n  }                                      \n": types.GetTestReportsDocument,
    "\n  query GetWeeklyLineChartData {\n    getWeeklyLineChartData {\n      template_name\n      total_count\n      weekday\n    }\n  }\n": types.GetWeeklyLineChartDataDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddDoctor($name: String!, $designation: String) {\n    addDoctor(name: $name, designation: $designation) {\n      id\n      name\n    }\n  }\n"): (typeof documents)["\n  mutation AddDoctor($name: String!, $designation: String) {\n    addDoctor(name: $name, designation: $designation) {\n      id\n      name\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddPatient(\n    $name: String!\n    $age: Int!\n    $gender: String!\n    $blood_group: String\n    $address: String,\n    $phone_number: String!\n  ) {\n    addPatient(\n      name: $name\n      age: $age\n      gender: $gender\n      blood_group: $blood_group\n      address: $address\n      phone_number: $phone_number\n    ) {\n      id\n      name\n      phone_number\n      age\n      gender\n      blood_group\n      address\n    }\n  }\n"): (typeof documents)["\n  mutation AddPatient(\n    $name: String!\n    $age: Int!\n    $gender: String!\n    $blood_group: String\n    $address: String,\n    $phone_number: String!\n  ) {\n    addPatient(\n      name: $name\n      age: $age\n      gender: $gender\n      blood_group: $blood_group\n      address: $address\n      phone_number: $phone_number\n    ) {\n      id\n      name\n      phone_number\n      age\n      gender\n      blood_group\n      address\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateOwnerWithEstablishment(\n    $username: String!\n    $password: String!\n    $establishment_name: String!\n    $establishment_phone_number: String!\n    $establishment_address: String\n    $wa_phone_number_id: String\n    $logo: Upload\n  ) {\n    createOwnerWithEstablishment(\n      username: $username\n      password: $password\n      establishment_name: $establishment_name\n      establishment_phone_number: $establishment_phone_number\n      establishment_address: $establishment_address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation CreateOwnerWithEstablishment(\n    $username: String!\n    $password: String!\n    $establishment_name: String!\n    $establishment_phone_number: String!\n    $establishment_address: String\n    $wa_phone_number_id: String\n    $logo: Upload\n  ) {\n    createOwnerWithEstablishment(\n      username: $username\n      password: $password\n      establishment_name: $establishment_name\n      establishment_phone_number: $establishment_phone_number\n      establishment_address: $establishment_address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateReport(\n    $template_id: Int!\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n    $pdf: Upload\n    $patient_id: Int!\n    $remarks: String\n    $staff: String\n    $collection_date: DateTime\n    $test_date: DateTime\n    $report_generation_date: DateTime\n    $doctor_id: Int\n  ) {\n    createReport(\n      template_id: $template_id\n      sections_input: $sections_input\n      fields_input: $fields_input\n      pdf: $pdf\n      patient_id: $patient_id\n      remarks: $remarks\n      staff: $staff\n      collection_date: $collection_date\n      test_date: $test_date\n      report_generation_date: $report_generation_date\n      doctor_id: $doctor_id\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation CreateReport(\n    $template_id: Int!\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n    $pdf: Upload\n    $patient_id: Int!\n    $remarks: String\n    $staff: String\n    $collection_date: DateTime\n    $test_date: DateTime\n    $report_generation_date: DateTime\n    $doctor_id: Int\n  ) {\n    createReport(\n      template_id: $template_id\n      sections_input: $sections_input\n      fields_input: $fields_input\n      pdf: $pdf\n      patient_id: $patient_id\n      remarks: $remarks\n      staff: $staff\n      collection_date: $collection_date\n      test_date: $test_date\n      report_generation_date: $report_generation_date\n      doctor_id: $doctor_id\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateTemplate(\n    $name: String!\n    $title_color: String\n    $report_name_color: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n  ) {\n    createTemplate(\n      name: $name\n      title_color: $title_color\n      report_name_color: $report_name_color\n      sections_input: $sections_input\n      fields_input: $fields_input\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation CreateTemplate(\n    $name: String!\n    $title_color: String\n    $report_name_color: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n  ) {\n    createTemplate(\n      name: $name\n      title_color: $title_color\n      report_name_color: $report_name_color\n      sections_input: $sections_input\n      fields_input: $fields_input\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteDoctor($id: Int!) {\n    deleteDoctor(id: $id) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteDoctor($id: Int!) {\n    deleteDoctor(id: $id) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeletePatient($id: Int!) {\n    deletePatient(id: $id) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation DeletePatient($id: Int!) {\n    deletePatient(id: $id) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteReport($id: Int!) {\n    deleteReport(id: $id) {\n      message\n    }\n  }                                   \n"): (typeof documents)["\n  mutation DeleteReport($id: Int!) {\n    deleteReport(id: $id) {\n      message\n    }\n  }                                   \n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteTemplate($id: Int!) {\n    deleteTemplate(id: $id) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteTemplate($id: Int!) {\n    deleteTemplate(id: $id) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Login(\n    $username: String!\n    $password: String!\n  ) {\n    login(username: $username, password: $password) {\n      token\n      user {\n        id\n        username\n        role\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation Login(\n    $username: String!\n    $password: String!\n  ) {\n    login(username: $username, password: $password) {\n      token\n      user {\n        id\n        username\n        role\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Logout {\n    logout {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation Logout {\n    logout {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation SendTestReport(\n    $report_id: Int!\n    $patient_id: Int!\n    $whatsapp_number: String\n    $pdf: Upload!\n  ) {\n    sendTestReport(\n      report_id: $report_id\n      patient_id: $patient_id\n      whatsapp_number: $whatsapp_number\n      pdf: $pdf\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation SendTestReport(\n    $report_id: Int!\n    $patient_id: Int!\n    $whatsapp_number: String\n    $pdf: Upload!\n  ) {\n    sendTestReport(\n      report_id: $report_id\n      patient_id: $patient_id\n      whatsapp_number: $whatsapp_number\n      pdf: $pdf\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateDoctor($id: Int!, $name: String, $designation: String) {\n    updateDoctor(id: $id, name: $name, designation: $designation) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateDoctor($id: Int!, $name: String, $designation: String) {\n    updateDoctor(id: $id, name: $name, designation: $designation) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateEstablishment(\n    $id: Int!\n    $establishment_name: String\n    $establishment_phone_number: String\n    $establishment_address: String\n    $wa_phone_number_id: String\n    $logo: Upload\n  ) {\n    updateEstablishment(\n      id: $id\n      establishment_name: $establishment_name\n      establishment_phone_number: $establishment_phone_number\n      establishment_address: $establishment_address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateEstablishment(\n    $id: Int!\n    $establishment_name: String\n    $establishment_phone_number: String\n    $establishment_address: String\n    $wa_phone_number_id: String\n    $logo: Upload\n  ) {\n    updateEstablishment(\n      id: $id\n      establishment_name: $establishment_name\n      establishment_phone_number: $establishment_phone_number\n      establishment_address: $establishment_address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateMyEstablishment(\n    $name: String\n    $phone_number: String\n    $address: String\n    $logo: Upload\n    $wa_phone_number_id: String\n  ) {\n    updateMyEstablishment(\n      name: $name\n      phone_number: $phone_number\n      address: $address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateMyEstablishment(\n    $name: String\n    $phone_number: String\n    $address: String\n    $logo: Upload\n    $wa_phone_number_id: String\n  ) {\n    updateMyEstablishment(\n      name: $name\n      phone_number: $phone_number\n      address: $address\n      wa_phone_number_id: $wa_phone_number_id\n      logo: $logo\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateMyProfile($username: String, $password: String) {\n    updateMyProfile(username: $username, password: $password) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateMyProfile($username: String, $password: String) {\n    updateMyProfile(username: $username, password: $password) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateOwner(\n    $id: Int!\n    $username: String!\n    $password: String!\n  ) {\n    updateOwner(\n      id: $id\n      username: $username\n      password: $password\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateOwner(\n    $id: Int!\n    $username: String!\n    $password: String!\n  ) {\n    updateOwner(\n      id: $id\n      username: $username\n      password: $password\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdatePatient(\n    $id: Int!\n    $name: String\n    $age: Int\n    $gender: String\n    $blood_group: String\n    $address: String,\n    $phone_number: String\n  ) {\n    updatePatient(\n      id: $id\n      name: $name\n      age: $age\n      gender: $gender\n      blood_group: $blood_group\n      address: $address\n      phone_number: $phone_number\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpdatePatient(\n    $id: Int!\n    $name: String\n    $age: Int\n    $gender: String\n    $blood_group: String\n    $address: String,\n    $phone_number: String\n  ) {\n    updatePatient(\n      id: $id\n      name: $name\n      age: $age\n      gender: $gender\n      blood_group: $blood_group\n      address: $address\n      phone_number: $phone_number\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateReport(\n    $id: Int!\n    $remarks: String\n    $staff: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n    $template_id: Int\n    $patient_id: Int\n    $collection_date: DateTime\n    $test_date: DateTime\n    $report_generation_date: DateTime\n    $doctor_id: Int\n  ) {\n    updateReport(\n      id: $id\n      remarks: $remarks\n      staff: $staff\n      sections_input: $sections_input\n      fields_input: $fields_input\n      template_id: $template_id\n      patient_id: $patient_id\n      collection_date: $collection_date\n      test_date: $test_date\n      report_generation_date: $report_generation_date\n      doctor_id: $doctor_id\n    ) {\n      message\n    }\n  }"): (typeof documents)["\n  mutation UpdateReport(\n    $id: Int!\n    $remarks: String\n    $staff: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n    $template_id: Int\n    $patient_id: Int\n    $collection_date: DateTime\n    $test_date: DateTime\n    $report_generation_date: DateTime\n    $doctor_id: Int\n  ) {\n    updateReport(\n      id: $id\n      remarks: $remarks\n      staff: $staff\n      sections_input: $sections_input\n      fields_input: $fields_input\n      template_id: $template_id\n      patient_id: $patient_id\n      collection_date: $collection_date\n      test_date: $test_date\n      report_generation_date: $report_generation_date\n      doctor_id: $doctor_id\n    ) {\n      message\n    }\n  }"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateTemplate(\n    $id: Int!\n    $name: String\n    $title_color: String\n    $report_name_color: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n  ) {\n    updateTemplate(\n      id: $id\n      name: $name\n      title_color: $title_color\n      report_name_color: $report_name_color\n      sections_input: $sections_input\n      fields_input: $fields_input\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateTemplate(\n    $id: Int!\n    $name: String\n    $title_color: String\n    $report_name_color: String\n    $sections_input: [SectionInput!]\n    $fields_input: [FieldInput!]\n  ) {\n    updateTemplate(\n      id: $id\n      name: $name\n      title_color: $title_color\n      report_name_color: $report_name_color\n      sections_input: $sections_input\n      fields_input: $fields_input\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpsertPdfSetting(\n    $establishment_id: Int\n    $title: String\n    $subtitle: String\n    $footer_left: String\n    $footer_right: String\n    $footer_center: String\n    $logo: Upload\n    $left_signature: Upload\n    $right_signature: Upload\n  ) {\n    upsertPdfSetting(\n      establishment_id: $establishment_id\n      title: $title\n      subtitle: $subtitle\n      footer_left: $footer_left\n      footer_right: $footer_right\n      footer_center: $footer_center\n      logo: $logo\n      left_signature: $left_signature\n      right_signature: $right_signature\n    ) {\n      message\n    }\n  }\n"): (typeof documents)["\n  mutation UpsertPdfSetting(\n    $establishment_id: Int\n    $title: String\n    $subtitle: String\n    $footer_left: String\n    $footer_right: String\n    $footer_center: String\n    $logo: Upload\n    $left_signature: Upload\n    $right_signature: Upload\n  ) {\n    upsertPdfSetting(\n      establishment_id: $establishment_id\n      title: $title\n      subtitle: $subtitle\n      footer_left: $footer_left\n      footer_right: $footer_right\n      footer_center: $footer_center\n      logo: $logo\n      left_signature: $left_signature\n      right_signature: $right_signature\n    ) {\n      message\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetEstablishmentDoctorList($first: Int!, $name: String, $page: Int) {\n    getEstablishmentDoctorList(\n      first: $first\n      name: $name\n      page: $page\n    ) {\n      data {\n        id\n        name\n        designation\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetEstablishmentDoctorList($first: Int!, $name: String, $page: Int) {\n    getEstablishmentDoctorList(\n      first: $first\n      name: $name\n      page: $page\n    ) {\n      data {\n        id\n        name\n        designation\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetEstablishmentList($first: Int!, $page: Int, $name: String) {\n    getEstablishmentList(\n      first: $first\n      page: $page\n      name: $name\n    ) {\n      data {\n        id\n        name\n        address\n        phone_number\n        owner {\n          id\n          username\n        }\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetEstablishmentList($first: Int!, $page: Int, $name: String) {\n    getEstablishmentList(\n      first: $first\n      page: $page\n      name: $name\n    ) {\n      data {\n        id\n        name\n        address\n        phone_number\n        owner {\n          id\n          username\n        }\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetMe {\n    getMe {\n      id\n      username\n      establishment {\n        id\n        name\n        address\n        phone_number\n        logo {\n          path\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetMe {\n    getMe {\n      id\n      username\n      establishment {\n        id\n        name\n        address\n        phone_number\n        logo {\n          path\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetPatientList($first: Int!, $page: Int, $keyword: String) {\n    getPatientList(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        name\n        age\n        gender\n        blood_group\n        address\n        phone_number\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetPatientList($first: Int!, $page: Int, $keyword: String) {\n    getPatientList(first: $first, page: $page, keyword: $keyword) {\n      data {\n        id\n        name\n        age\n        gender\n        blood_group\n        address\n        phone_number\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetPdfSetting {\n    getPdfSetting {\n      id\n      establishment_id\n      title\n      subtitle\n      footer_left\n      footer_right\n      footer_center\n      logo {\n        id\n        path\n        hash\n        order\n      }\n      leftSignature {\n        id\n        path\n        hash\n      }\n      rightSignature {\n        id\n        path\n        hash\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetPdfSetting {\n    getPdfSetting {\n      id\n      establishment_id\n      title\n      subtitle\n      footer_left\n      footer_right\n      footer_center\n      logo {\n        id\n        path\n        hash\n        order\n      }\n      leftSignature {\n        id\n        path\n        hash\n      }\n      rightSignature {\n        id\n        path\n        hash\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetRecentReports($first: Int!) {\n    getRecentReports(first: $first) {\n      id\n      patient {\n        id\n        name\n      }\n      template {\n        id\n        name\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetRecentReports($first: Int!) {\n    getRecentReports(first: $first) {\n      id\n      patient {\n        id\n        name\n      }\n      template {\n        id\n        name\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetReportById($id: Int!) {\n    getReportById(id: $id) {\n      id\n      remarks\n      staff\n      collection_date\n      test_date\n      report_generation_date\n      patient {\n        id\n        name\n        phone_number\n        age\n        gender\n        address\n        blood_group\n      }\n      template_data {\n        fields {\n          name\n          name_value\n          unit\n          unit_value\n          reference\n          reference_value\n          input_type\n          input_type_values\n          sub_fields {\n            name\n            name_value\n            input_type\n            input_type_values\n          }\n        }\n        sections {\n          name\n          name_value\n          fields {\n            name\n            name_value\n            unit\n            unit_value\n            unit_active\n            reference\n            reference_active\n            reference_value\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              name_value\n              input_type\n              input_type_values\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n            }\n          }\n        }\n      }\n      template {\n        id\n        name\n        title_color\n        report_name_color\n        template_data {\n          fields {\n            name\n            unit\n            reference\n            input_type\n          }\n          sections {\n            name\n            name_value\n            fields {\n              name\n              name_value\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_active\n              reference_value\n              input_type\n              input_type_values\n              sub_fields {\n                name\n                name_value\n                input_type\n                input_type_values\n                unit\n                unit_value\n                unit_active\n                reference\n                reference_value\n                reference_active\n              }\n            }\n          }\n        }\n      }\n      doctor {\n        id\n        name\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetReportById($id: Int!) {\n    getReportById(id: $id) {\n      id\n      remarks\n      staff\n      collection_date\n      test_date\n      report_generation_date\n      patient {\n        id\n        name\n        phone_number\n        age\n        gender\n        address\n        blood_group\n      }\n      template_data {\n        fields {\n          name\n          name_value\n          unit\n          unit_value\n          reference\n          reference_value\n          input_type\n          input_type_values\n          sub_fields {\n            name\n            name_value\n            input_type\n            input_type_values\n          }\n        }\n        sections {\n          name\n          name_value\n          fields {\n            name\n            name_value\n            unit\n            unit_value\n            unit_active\n            reference\n            reference_active\n            reference_value\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              name_value\n              input_type\n              input_type_values\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n            }\n          }\n        }\n      }\n      template {\n        id\n        name\n        title_color\n        report_name_color\n        template_data {\n          fields {\n            name\n            unit\n            reference\n            input_type\n          }\n          sections {\n            name\n            name_value\n            fields {\n              name\n              name_value\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_active\n              reference_value\n              input_type\n              input_type_values\n              sub_fields {\n                name\n                name_value\n                input_type\n                input_type_values\n                unit\n                unit_value\n                unit_active\n                reference\n                reference_value\n                reference_active\n              }\n            }\n          }\n        }\n      }\n      doctor {\n        id\n        name\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetStats($start_date: DateTime, $end_date: DateTime) {\n    getStats(start_date: $start_date, end_date: $end_date) {\n      total_patient\n      total_tests_count\n    }\n  }\n"): (typeof documents)["\n  query GetStats($start_date: DateTime, $end_date: DateTime) {\n    getStats(start_date: $start_date, end_date: $end_date) {\n      total_patient\n      total_tests_count\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetTemplateBySlug($slug: String!) {\n    getTemplateBySlug(slug: $slug) {\n      id\n      name\n      title_color\n      report_name_color\n      template_data {\n        fields {\n          name\n          unit\n          reference\n          input_type\n        }\n        sections {\n          name\n          fields {\n            name\n            unit\n            unit_active\n            reference\n            reference_active\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              input_type\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n            }\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetTemplateBySlug($slug: String!) {\n    getTemplateBySlug(slug: $slug) {\n      id\n      name\n      title_color\n      report_name_color\n      template_data {\n        fields {\n          name\n          unit\n          reference\n          input_type\n        }\n        sections {\n          name\n          fields {\n            name\n            unit\n            unit_active\n            reference\n            reference_active\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              input_type\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n            }\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetTemplates(\n    $name: String\n    $first: Int!\n    $page: Int\n  ) {\n    getTemplates(\n      name: $name\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        name\n        slug\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n  "): (typeof documents)["\n  query GetTemplates(\n    $name: String\n    $first: Int!\n    $page: Int\n  ) {\n    getTemplates(\n      name: $name\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        name\n        slug\n      }\n      paginator_info {\n        total\n      }\n    }\n  }\n  "];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetTestReports(\n    $first: Int!\n    $page: Int\n    $start_date: DateTime\n    $end_date: DateTime\n    $report_name: String\n    $patient_info: String\n  ) {\n    getTestReports(\n      first: $first\n      page: $page\n      start_date: $start_date\n      end_date: $end_date\n      report_name: $report_name\n      patient_info: $patient_info\n    ) {\n      data {\n        id\n        patient {\n          id\n          name\n          phone_number\n          address\n          age\n          gender\n          blood_group\n        }\n        template {\n          id\n          name\n          slug\n          title_color\n          report_name_color\n        }\n        template_data {\n          fields {\n            name\n            name_value\n            unit\n            unit_value\n            reference\n            reference_value\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              name_value\n              input_type\n              input_type_values\n            }\n          }\n          sections {\n            name\n            name_value\n            fields {\n              name\n              name_value\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n              input_type\n              input_type_values\n              sub_fields {\n                name\n                name_value\n                input_type\n                input_type_values\n                unit\n                unit_value\n                unit_active\n                reference\n                reference_value\n                reference_active\n              }\n            }\n          }\n        }\n        doctor {\n          id\n          name\n        }\n        latestWhatsAppReceipt {\n          message_status\n          error_message\n        }\n        created_at\n        remarks\n        staff\n        collection_date\n        report_generation_date\n        test_date\n      }\n      paginator_info {\n        total\n      }\n    }\n  }                                      \n"): (typeof documents)["\n  query GetTestReports(\n    $first: Int!\n    $page: Int\n    $start_date: DateTime\n    $end_date: DateTime\n    $report_name: String\n    $patient_info: String\n  ) {\n    getTestReports(\n      first: $first\n      page: $page\n      start_date: $start_date\n      end_date: $end_date\n      report_name: $report_name\n      patient_info: $patient_info\n    ) {\n      data {\n        id\n        patient {\n          id\n          name\n          phone_number\n          address\n          age\n          gender\n          blood_group\n        }\n        template {\n          id\n          name\n          slug\n          title_color\n          report_name_color\n        }\n        template_data {\n          fields {\n            name\n            name_value\n            unit\n            unit_value\n            reference\n            reference_value\n            input_type\n            input_type_values\n            sub_fields {\n              name\n              name_value\n              input_type\n              input_type_values\n            }\n          }\n          sections {\n            name\n            name_value\n            fields {\n              name\n              name_value\n              unit\n              unit_value\n              unit_active\n              reference\n              reference_value\n              reference_active\n              input_type\n              input_type_values\n              sub_fields {\n                name\n                name_value\n                input_type\n                input_type_values\n                unit\n                unit_value\n                unit_active\n                reference\n                reference_value\n                reference_active\n              }\n            }\n          }\n        }\n        doctor {\n          id\n          name\n        }\n        latestWhatsAppReceipt {\n          message_status\n          error_message\n        }\n        created_at\n        remarks\n        staff\n        collection_date\n        report_generation_date\n        test_date\n      }\n      paginator_info {\n        total\n      }\n    }\n  }                                      \n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetWeeklyLineChartData {\n    getWeeklyLineChartData {\n      template_name\n      total_count\n      weekday\n    }\n  }\n"): (typeof documents)["\n  query GetWeeklyLineChartData {\n    getWeeklyLineChartData {\n      template_name\n      total_count\n      weekday\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;