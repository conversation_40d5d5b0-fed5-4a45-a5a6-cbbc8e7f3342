import type { GetPdfSettingQuery, TestReport } from '~/gql/graphql'
import type { DeepPartial } from '~/lib/types/deep-partial'
import { pdf } from '@react-pdf/renderer'
import { useForm } from '@tanstack/react-form'
import { toast } from 'sonner'
import { z } from 'zod'
import useSendTestReport from '~/hooks/use-send-test-report'
import baseUrl from '~/lib/base-url'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { Button } from '../ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import FormItem from '../ui/form-item'
import FormMessage from '../ui/form-message'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import ReportPDFDocument from './report-pdf'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  report: DeepPartial<TestReport>
  pdfSettings: GetPdfSettingQuery['getPdfSetting'] | null | undefined
  hideEmptyFields?: boolean
}

const schema = z.object({
  phone_number: z
    .string()
    .min(10, 'Phone number must be at least 10 characters')
    .max(10, 'Phone number must be at most 10 characters'),
})

type FormType = z.infer<typeof schema>

export default function SendTestReportDialog({
  open,
  onOpenChange,
  report,
  pdfSettings,
  hideEmptyFields,
}: Props) {
  const { sendTestReport } = useSendTestReport()

  const form = useForm({
    defaultValues: {
      phone_number: report.patient?.phone_number || '',
    } as FormType,
    validators: {
      onSubmit: schema,
    },
    onSubmit: async ({ value }) => {
      const imagePath = pdfSettings?.logo?.path || ''
      const signaturePath = pdfSettings?.establishment?.signature?.path || ''
      let signatureData: string | undefined
      let imgData: string | undefined

      if (signaturePath) {
        const response = await fetch(`${baseUrl}/image/medium/${signaturePath}`)
        const imageBlob = await response.blob()
        signatureData = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onloadend = () => {
            resolve(reader.result as string)
          }
          reader.readAsDataURL(imageBlob)
        })
      }

      if (imagePath) {
        const response = await fetch(`${baseUrl}/image/medium/${imagePath}`)
        const imageBlob = await response.blob()
        imgData = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onloadend = () => {
            resolve(reader.result as string)
          }
          reader.readAsDataURL(imageBlob)
        })
      }
      const blob = await pdf(
        <ReportPDFDocument
          signatureData={signatureData}
          reportData={report}
          pdfSettings={pdfSettings}
          imgData={imgData}
          hideEmptyFields={hideEmptyFields}
        />,
      ).toBlob()
      const pdfFile = new File([blob], `report-${report.id}.pdf`, {
        type: blob.type,
      })

      sendTestReport.mutate(
        {
          report_id: report.id!,
          patient_id: report.patient!.id!,
          pdf: pdfFile,
          whatsapp_number: value.phone_number,
        },
        {
          onSuccess: () => {
            toast.success('Report sent successfully')
            form.reset()
            onOpenChange(false)
          },
          onError: (error) => {
            toast.error(parseGraphqlError(error))
          },
        },
      )
    },
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Send Test Report</DialogTitle>
          <DialogDescription>Enter phone number</DialogDescription>
        </DialogHeader>
        <form
          className="flex flex-col gap-4"
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
        >
          <form.Field
            name="phone_number"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Phone number</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Phone number"
                  maxLength={10}
                />
                <FormMessage
                  errors={state.meta.errors.map(e => e?.message).join(', ')}
                />
              </FormItem>
            )}
          />
          <DialogFooter>
            <Button type="submit" isLoading={sendTestReport.isPending}>
              Send Report
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
