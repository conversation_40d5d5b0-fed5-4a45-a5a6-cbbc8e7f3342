import type { AddDoctorType } from '~/lib/types/doctor-schema'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '~/components/ui/dialog'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import useAddDoctor from '~/hooks/use-add-doctor'
import useDialogStates from '~/hooks/use-dialog-states'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { AddDoctorSchema } from '~/lib/types/doctor-schema'
import { cn } from '~/lib/utils'

interface Props {
  onSuccessCallback?: ({ id, name }: { id: string, name: string }) => void
}

export default function AddDoctorDialog({ onSuccessCallback }: Props) {
  const { open, toggleDialog: onOpenChange } = useDialogStates()
  const queryClient = useQueryClient()
  const { addDoctor } = useAddDoctor()

  const form = useForm({
    defaultValues: {
      name: '',
      designation: '',
    } as AddDoctorType,
    validators: {
      onSubmit: AddDoctorSchema,
    },
    onSubmit: async ({ value }) => {
      addDoctor.mutate(value, {
        onSuccess: (data) => {
          form.reset()
          toast.success('Doctor added successfully')
          onOpenChange(false)
          queryClient.invalidateQueries({ queryKey: ['get-establishment-doctor-list'] })
          if (onSuccessCallback) {
            const id = data?.addDoctor?.id
            onSuccessCallback({
              id: id.toString(),
              name: data?.addDoctor?.name,
            })
          }
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
        },
      })
    },
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button
          className={cn('', {
            'border-0': onSuccessCallback,
          })}
          variant={onSuccessCallback ? 'outline' : 'default'}
        >
          Add Doctor
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add new doctor</DialogTitle>
          <DialogDescription>Enter doctor details</DialogDescription>
        </DialogHeader>
        <form
          className="flex flex-col gap-4"
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
        >
          <form.Field
            name="name"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Name</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Doctor Name"
                />
                <FormMessage
                  errors={state.meta.errors.map(e => e?.message).join(', ')}
                />
              </FormItem>
            )}
          />
          <form.Field
            name="designation"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Designation</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Doctor Designation"
                />
                <FormMessage
                  errors={state.meta.errors.map(e => e?.message).join(', ')}
                />
              </FormItem>
            )}
          />
          <DialogFooter>
            <Button isLoading={addDoctor.isPending} type="submit">Submit</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
