import type { Editor } from '@tiptap/react'
import UnderlineExtension from '@tiptap/extension-underline'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Strikethrough,
  Underline,
} from 'lucide-react'
import { useEffect } from 'react'
import { Toggle } from '~/components/ui/toggle'

interface Props {
  id: string
  name: string
  value?: string
  onChange?: (value: string) => void
  tabIndex?: number
}

function Tiptap({ id, name, value, onChange, tabIndex, ...props }: Props) {
  const editor = useEditor({
    extensions: [StarterKit, UnderlineExtension],
    content: value,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML())
    },
    editorProps: {
      attributes: {
        class: 'editor-base',
        spellcheck: 'false',
      },
    },
  })

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || '')
    }
  }, [value, editor])

  return (
    <>
      <RichTextEditorToolbar editor={editor} />
      <EditorContent
        className="prose prose-sm dark:prose-invert max-w-none focus:outline-none"
        editor={editor}
      />
      {/* Hidden input to ensure form submission includes the TipTap content */}
      <input
        {...props}
        tabIndex={tabIndex}
        id={id}
        name={name}
        type="hidden"
        value={editor?.getHTML() || ''}
      />
    </>
  )
}

export default Tiptap

function RichTextEditorToolbar({ editor }: { editor: Editor | null }) {
  return (
    editor && (
      <div className="border border-input bg-transparent rounded-tr-md rounded-tl-md p-1 flex flex-row items-center gap-1 flex-wrap">
        {/* Text formatting section */}
        <div className="flex flex-row items-center gap-1">
          <Toggle
            onPressedChange={() => editor.chain().focus().toggleBold().run()}
            pressed={editor.isActive('bold')}
            size="sm"
          >
            <Bold className="h-4 w-4" />
          </Toggle>
          <Toggle
            onPressedChange={() => editor.chain().focus().toggleItalic().run()}
            pressed={editor.isActive('italic')}
            size="sm"
          >
            <Italic className="h-4 w-4" />
          </Toggle>
          <Toggle
            onPressedChange={() =>
              editor.chain().focus().toggleUnderline().run()}
            pressed={editor.isActive('underline')}
            size="sm"
          >
            <Underline className="h-4 w-4" />
          </Toggle>
          <Toggle
            onPressedChange={() => editor.chain().focus().toggleStrike().run()}
            pressed={editor.isActive('strike')}
            size="sm"
          >
            <Strikethrough className="h-4 w-4" />
          </Toggle>
        </div>

        {/* List formatting section */}
        <div className="flex flex-row items-center gap-1 ml-1">
          <Toggle
            onPressedChange={() =>
              editor.chain().focus().toggleBulletList().run()}
            pressed={editor.isActive('bulletList')}
            size="sm"
          >
            <List className="h-4 w-4" />
          </Toggle>
          <Toggle
            onPressedChange={() =>
              editor.chain().focus().toggleOrderedList().run()}
            pressed={editor.isActive('orderedList')}
            size="sm"
          >
            <ListOrdered className="h-4 w-4" />
          </Toggle>
        </div>
      </div>
    )
  )
}
