import { ChevronsUpDown } from 'lucide-react'
import { useState } from 'react'
import { Button } from '~/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '~/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import useDialogStates from '~/hooks/use-dialog-states'
import useGetEstablishmentDoctorList from '~/hooks/use-get-establishment-doctor-list'
import AddDoctorDialog from './add-doctor-dialog'

interface Props {
  onSelect: (id: string) => void
  selectedId?: string
}

export default function RefDoctorCombobox({ onSelect, selectedId }: Props) {
  const { open, toggleDialog } = useDialogStates()
  const [value, setValue] = useState(selectedId ?? '')

  const [newDoctor, setNewDoctor] = useState('') // used to display correct name when creating a new doctor

  const { data, isLoading, isError, search, handleSearch } = useGetEstablishmentDoctorList()

  return (
    <Popover open={open} onOpenChange={toggleDialog}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {newDoctor || (value
            ? data?.getEstablishmentDoctorList?.data?.find(doctor => doctor.id.toString() === value)?.name
            : 'Select doctor')}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command shouldFilter={false}>
          <CommandInput isLoading={isLoading} value={search} onValueChange={value => handleSearch(value)} placeholder="Search doctor..." />
          <AddDoctorDialog onSuccessCallback={({ id, name }) => {
            onSelect(id)
            setValue(id)
            setNewDoctor(name)
            toggleDialog(false)
          }}
          />
          <CommandList>
            {!isLoading && (<CommandEmpty>No doctor found.</CommandEmpty>)}
            {isError && (
              <div
                className="py-6 text-center text-sm"
              >
                Error fetching doctor list.
              </div>
            )}
            <CommandGroup>
              {data?.getEstablishmentDoctorList?.data && data.getEstablishmentDoctorList.data?.map(doctor => (
                <CommandItem
                  key={doctor.id}
                  value={doctor.id.toString()}
                  onSelect={(currentValue) => {
                    onSelect(currentValue)
                    setValue(currentValue === value ? '' : currentValue)
                    toggleDialog(false)
                  }}
                >
                  <div className="flex justify-between w-full">
                    <span>{doctor.name}</span>
                    {doctor.designation
                      ? (
                          <span className="text-sm text-gray-500">
                            {doctor.designation}
                          </span>
                        )
                      : null}
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
