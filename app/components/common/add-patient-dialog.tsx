import type { AddPatientType } from '~/lib/types/add-patient-schema'
import type { SelectedPatient } from '~/lib/types/selected-patient'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import useAddPatient from '~/hooks/use-add-patient'
import useDialogStates from '~/hooks/use-dialog-states'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { AddPatientSchema } from '~/lib/types/add-patient-schema'

interface Props {
  onSuccessCallback?: (patient: SelectedPatient) => void
}

export default function AddPatientDialog({ onSuccessCallback }: Props) {
  const { open, toggleDialog, closeDialog } = useDialogStates()
  const queryClient = useQueryClient()
  const { addPatient } = useAddPatient()

  const form = useForm({
    defaultValues: {
      name: '',
      age: '' as unknown as number,
      gender: '',
      blood_group: '',
      address: '',
      phone_number: '',
    } as AddPatientType,
    validators: {
      onSubmit: AddPatientSchema,
    },
    onSubmit: ({ value }) => {
      addPatient.mutate(value, {
        onSuccess: (data) => {
          toast.success('Patient added successfully')
          queryClient.invalidateQueries({ queryKey: ['patient-list'] })
          closeDialog()
          form.reset()
          if (onSuccessCallback) {
            onSuccessCallback({
              id: data.addPatient.id,
              name: data.addPatient.name,
              age: data.addPatient.age,
              gender: data.addPatient.gender,
              blood_group: data.addPatient.blood_group,
              address: data.addPatient.address,
              phone_number: data.addPatient.phone_number,
            })
          }
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
        },
      })
    },
  })

  return (
    <Dialog open={open} onOpenChange={toggleDialog}>
      <DialogTrigger asChild>
        <Button>
          Add New Patient
        </Button>
      </DialogTrigger>
      <DialogContent className="min-w-2xl w-full">
        <DialogHeader>
          <DialogTitle>
            Add New Patient
          </DialogTitle>
          <DialogDescription>
            Enter patient details
          </DialogDescription>
        </DialogHeader>
        <form
          className="flex flex-col gap-4"
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
        >
          <form.Field
            name="name"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Name</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Patient Name"
                />
                <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          />
          <form.Field
            name="phone_number"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Phone number</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Phone number"
                  maxLength={10}
                />
                <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          />
          <form.Field
            name="address"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Address</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Address"
                />
                <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          />
          <div className="flex gap-4">
            <form.Field
              name="age"
              children={({ state, handleBlur, handleChange }) => (
                <FormItem>
                  <Label>Age</Label>
                  <Input
                    value={state.value}
                    onBlur={handleBlur}
                    onChange={e => handleChange(Number(e.target.value))}
                    placeholder="Age"
                  />
                  <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
                </FormItem>
              )}
            />
            <form.Field
              name="gender"
              children={({ state, handleBlur, handleChange }) => (
                <FormItem>
                  <Label>Gender</Label>
                  <Input
                    value={state.value}
                    onBlur={handleBlur}
                    onChange={e => handleChange(e.target.value)}
                    placeholder="Gender"
                  />
                  <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
                </FormItem>
              )}
            />
            <form.Field
              name="blood_group"
              children={({ state, handleBlur, handleChange }) => (
                <FormItem>
                  <Label>Blood group</Label>
                  <Input
                    value={state.value}
                    onBlur={handleBlur}
                    onChange={e => handleChange(e.target.value)}
                    placeholder="Blood group"
                  />
                  <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
                </FormItem>
              )}
            />

          </div>
          <DialogFooter className="gap-x-8">
            <Button type="button" variant="secondary" onClick={closeDialog}>Cancel</Button>
            <Button isLoading={addPatient.isPending} type="submit">Add Patient</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
