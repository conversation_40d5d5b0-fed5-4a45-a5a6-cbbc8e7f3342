import { ChevronLeft } from 'lucide-react'
import { useNavigate } from 'react-router'

function BackButton() {
  const navigate = useNavigate()
  return (
    <div>
      <button
        type="button"
        className="-ml-2 flex items-center"
        onClick={async () => {
          await navigate(-1)
        }}
      >
        <ChevronLeft className="size-8" /> Back
      </button>
    </div>
  )
}

export default BackButton
