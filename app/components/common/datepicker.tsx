import { format } from 'date-fns'
import { CalendarIcon } from 'lucide-react'
import useDialogStates from '~/hooks/use-dialog-states'
import { cn } from '~/lib/utils'
import { Button } from '../ui/button'
import { Calendar } from '../ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover'

interface Props {
  value: string | undefined
  handleDate: (date: string | undefined) => void
  placeholder?: string
}

export default function DatePicker({ value, handleDate, placeholder }: Props) {
  // Convert string value to Date object for the Calendar component
  const selectedDate = value ? new Date(value) : undefined

  const { open, toggleDialog } = useDialogStates()

  const handleSelectDate = (date: Date | undefined) => {
    toggleDialog(false)
    handleDate(date ? format(date, 'yyyy-MM-dd') : undefined)
  }

  return (
    <Popover open={open} onOpenChange={toggleDialog}>
      <PopoverTrigger asChild>
        <Button
          className={cn(
            'pl-3 text-left font-normal w-full',
            !value && 'text-muted-foreground',
          )}
          type="button"
          variant="outline"
        >
          {/* Display formatted date if value is a valid date string */}
          {value && selectedDate && !Number.isNaN(selectedDate.getTime())
            ? (
                format(selectedDate, 'PPP')
              )
            : (
                <span>{placeholder ?? 'Pick a date'}</span>
              )}
          <CalendarIcon className="ml-auto size-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-auto p-0">
        <Calendar
          mode="single"
          // Convert selected Date object back to string before calling handleDate
          onSelect={handleSelectDate}
          selected={selectedDate}
        />
      </PopoverContent>
    </Popover>
  )
}
