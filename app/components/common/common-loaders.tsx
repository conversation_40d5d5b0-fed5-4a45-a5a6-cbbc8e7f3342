import { Skeleton } from '~/components/ui/skeleton'
import { cn } from '~/lib/utils'

interface Props {
  className?: string
  length?: number
}

export function TableLoader({ className, length }: Props) {
  return (
    <div className={cn('flex flex-col gap-y-4', className)}>
      {Array.from({ length: length || 5 }).map((_, index) => (
        <Skeleton className="h-8" key={index} />
      ))}
    </div>
  )
}

export function CardLoader({ className, length }: Props) {
  return (
    <div className={cn(className)}>
      {Array.from({ length: length || 9 }).map((_, index) => (
        <Skeleton className="size-48" key={index} />
      ))}
    </div>
  )
}
