import { Toolt<PERSON>, <PERSON><PERSON><PERSON>Content, <PERSON>ltipProvider, TooltipTrigger } from '~/components/ui/tooltip'

interface Props {
  children: React.ReactNode
  message: string
}

export default function CustomTooltip({ children, message }: Props) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {children}
        </TooltipTrigger>
        <TooltipContent className="whitespace-pre-wrap">
          {message}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
