import { createCookieSessionStorage } from 'react-router'

interface SessionData {
  role: string
  token: string
}

interface SessionFlashData {
  error: string
  successMessage: string
}

const { commitSession, destroySession, getSession }
  = createCookieSessionStorage<SessionData, SessionFlashData>({
    cookie: {
      httpOnly: true,
      maxAge: 82_800_000, // 23 hours
      name: '__medlab_session',
      path: '/',
      secrets: ['m3dlabs3kret'],
      secure: process.env.NODE_ENV === 'production',
    },
  })

export { commitSession, destroySession, getSession }
