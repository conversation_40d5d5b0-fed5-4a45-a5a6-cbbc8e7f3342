import { FieldInputType } from '~/gql/graphql'

export default function parseFieldInputType(input: string): FieldInputType {
  switch (input.toUpperCase()) {
    case 'TEXTFIELD':
      return FieldInputType.Textfield
    case 'RICHTEXT':
      return FieldInputType.Richtext
    case 'SELECT':
      return FieldInputType.Select
    case 'MULTISELECT':
      return FieldInputType.Multiselect
    case 'SUBFIELD':
      return FieldInputType.Subfield
    default:
      throw new Error(`Unknown FieldInputType: ${input}`)
  }
}
