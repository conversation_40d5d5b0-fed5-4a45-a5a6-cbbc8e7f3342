import { z } from 'zod'
import type { <PERSON> } from '~/gql/graphql'

export const AddDoctorSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  designation: z.string().optional(),
})

export const UpdateDoctorSchema = z.object({
  name: z.string().optional(),
  designation: z.string().optional(),
})

export type UpdateDoctorType = z.infer<typeof UpdateDoctorSchema>
export type AddDoctorType = z.infer<typeof AddDoctorSchema>

export type SelectedDoctor = Pick<Doctor, 'id' | 'name' | 'designation'> 