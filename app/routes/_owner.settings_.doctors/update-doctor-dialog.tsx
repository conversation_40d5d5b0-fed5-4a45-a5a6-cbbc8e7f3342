import type {
  SelectedDoctor,
  UpdateDoctorType,
} from '~/lib/types/doctor-schema'
import { Label } from '@radix-ui/react-label'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UpdateDoctorSchema } from '~/lib/types/doctor-schema'
import useUpdateDoctor from './use-update-doctor'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  doctor: SelectedDoctor
}

export default function UpdateDoctorDialog({
  open,
  onO<PERSON><PERSON><PERSON><PERSON>,
  doctor,
}: Props) {
  const queryClient = useQueryClient()
  const { updateDoctor } = useUpdateDoctor()

  const form = useForm({
    defaultValues: {
      name: doctor.name,
      designation: doctor.designation ?? '',
    } as UpdateDoctorType,
    validators: {
      onSubmit: UpdateDoctorSchema,
    },
    onSubmit: async ({ value }) => {
      updateDoctor.mutate(
        { id: doctor.id, data: value },
        {
          onSuccess: () => {
            form.reset()
            toast.success('Doctor updated successfully')
            onOpenChange(false)
            queryClient.invalidateQueries({
              queryKey: ['get-establishment-doctor-list'],
            })
          },
          onError: (error) => {
            toast.error(parseGraphqlError(error))
          },
        },
      )
    },
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update doctor</DialogTitle>
          <DialogDescription>Enter doctor details</DialogDescription>
        </DialogHeader>
        <form
          className="flex flex-col gap-4"
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
        >
          <form.Field
            name="name"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Name</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Doctor Name"
                />
                <FormMessage
                  errors={state.meta.errors.map(e => e?.message).join(', ')}
                />
              </FormItem>
            )}
          />
          <form.Field
            name="designation"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Designation</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Doctor Designation"
                />
                <FormMessage
                  errors={state.meta.errors.map(e => e?.message).join(', ')}
                />
              </FormItem>
            )}
          />
          <DialogFooter>
            <Button isLoading={updateDoctor.isPending} type="submit">
              Submit
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
