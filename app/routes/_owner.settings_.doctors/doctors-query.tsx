import { Input } from '~/components/ui/input'
import useGetEstablishmentDoctorList from '~/hooks/use-get-establishment-doctor-list'

export default function DoctorsQuery() {
  const { search, handleSearch } = useGetEstablishmentDoctorList()

  return (
    <div className="flex gap-x-4 my-4">
      <Input
        className="basis-full md:basis-1/5"
        value={search}
        onChange={e => handleSearch(e.target.value)}
        placeholder="Search by doctor name"
      />
    </div>
  )
}
