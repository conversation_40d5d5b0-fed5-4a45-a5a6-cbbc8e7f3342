import { useMutation } from '@tanstack/react-query'
import { DELETE_DOCTOR } from '~/graphql/mutations/delete-doctor'
import { graphqlClient } from '~/lib/graphql-client'

export default function useDeleteDoctor() {
  const deleteDoctor = useMutation({
    mutationFn: async (id: number) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: DELETE_DOCTOR,
        variables: {
          id,
        },
      })
    },
  })

  return { deleteDoctor }
}
