import type { SelectedDoctor } from '~/lib/types/doctor-schema'
import { useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { toast } from 'sonner'
import CommonError from '~/components/common/common-error'
import { TableLoader } from '~/components/common/common-loaders'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import CustomTooltip from '~/components/common/custom-tooltip'
import PagePagination from '~/components/common/page-pagination'
import DeleteIcon from '~/components/icons/delete-icon'
import UpdateIcon from '~/components/icons/update-icon'
import { Button } from '~/components/ui/button'
import { ScrollArea } from '~/components/ui/scroll-area'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import useDialogStates from '~/hooks/use-dialog-states'
import useGetEstablishmentDoctorList from '~/hooks/use-get-establishment-doctor-list'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import UpdateDoctorDialog from './update-doctor-dialog'
import useDeleteDoctor from './use-delete-doctor'

export default function DoctorsTable() {
  const [selectedDoctor, setSelectedDoctor] = useState<SelectedDoctor | null>(
    null,
  )
  const queryClient = useQueryClient()

  const { deleteDoctor } = useDeleteDoctor()

  const { open: openDeleteDialog, toggleDialog: toggleDeleteDialog }
    = useDialogStates()
  const { open: openUpdateDialog, toggleDialog: toggleUpdateDialog }
    = useDialogStates()

  const { data, isLoading, isError, totalPages, handlePageChange, page }
    = useGetEstablishmentDoctorList()

  const handleDeleteDoctor = () => {
    if (!selectedDoctor)
      return
    // Assuming you have a deleteDoctor mutation defined
    deleteDoctor.mutate(selectedDoctor.id, {
      onSuccess: () => {
        toast.success('Doctor deleted successfully')
        toggleDeleteDialog(false)
        queryClient.invalidateQueries({
          queryKey: ['get-establishment-doctor-list'],
        })
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  if (isError)
    return <CommonError message="Error fetching doctor list" />

  return (
    <>
      <div className="flex flex-col grow">
        {data?.getEstablishmentDoctorList?.data
          && data.getEstablishmentDoctorList.data.length === 0
          ? (
              <div className="text-center font-bold text-xl">No doctors found</div>
            )
          : (
              <ScrollArea className="w-full">
                <Table className="min-w-320">
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-1/3">Name</TableHead>
                      <TableHead className="w-1/3">Designation</TableHead>
                      <TableHead className="w-1/3 text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading
                      ? (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center">
                              <TableLoader length={15} />
                            </TableCell>
                          </TableRow>
                        )
                      : (
                          data?.getEstablishmentDoctorList.data?.map(doctor => (
                            <TableRow key={doctor.id}>
                              <TableCell>{doctor.name}</TableCell>
                              <TableCell>{doctor.designation ?? '-'}</TableCell>
                              <TableCell>
                                <div className="flex gap-x-2 justify-end">
                                  <CustomTooltip message="Update doctor">
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      onClick={() => {
                                        setSelectedDoctor(doctor)
                                        toggleUpdateDialog(true)
                                      }}
                                    >
                                      <UpdateIcon />
                                    </Button>
                                  </CustomTooltip>
                                  <CustomTooltip message="Delete doctor">
                                    <Button
                                      onClick={() => {
                                        setSelectedDoctor(doctor)
                                        toggleDeleteDialog(true)
                                      }}
                                      variant="outline"
                                      size="icon"
                                    >
                                      <DeleteIcon />
                                    </Button>
                                  </CustomTooltip>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                  </TableBody>
                </Table>
              </ScrollArea>
            )}
      </div>
      {totalPages > 1
        ? (
            <PagePagination
              currentPage={page}
              handlePagePagination={handlePageChange}
              lastPage={totalPages}
            />
          )
        : null}

      {selectedDoctor && (
        <ConfirmationDialog
          handleConfirm={handleDeleteDoctor}
          handleOpenChange={toggleDeleteDialog}
          isPending={deleteDoctor.isPending}
          open={openDeleteDialog}
        />
      )}
      {selectedDoctor && (
        <UpdateDoctorDialog
          open={openUpdateDialog}
          onOpenChange={(open) => {
            if (!open) {
              setSelectedDoctor(null)
            }
            toggleUpdateDialog(open)
          }}
          doctor={selectedDoctor}
        />
      )}
    </>
  )
}
