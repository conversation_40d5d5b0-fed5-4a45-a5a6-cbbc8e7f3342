import type { UpdateDoctorType } from '~/lib/types/doctor-schema'
import { useMutation } from '@tanstack/react-query'
import { UPDATE_DOCTOR } from '~/graphql/mutations/update-doctor'
import { graphqlClient } from '~/lib/graphql-client'

export default function useUpdateDoctor() {
  const updateDoctor = useMutation({
    mutationFn: async ({ data, id }: { data: UpdateDoctorType, id: number }) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: UPDATE_DOCTOR,
        variables: {
          id,
          ...data,
        },
      })
    },
  })

  return { updateDoctor }
}
