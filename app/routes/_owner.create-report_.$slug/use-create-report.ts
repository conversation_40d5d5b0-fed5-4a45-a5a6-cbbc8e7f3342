import type { CreateReportType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { format } from 'date-fns'
import { CREATE_REPORT } from '~/graphql/mutations/create-report'
import { graphqlClient } from '~/lib/graphql-client'

export default function useCreateReport() {
  const createReport = useMutation({
    mutationFn: async ({ data, patient_id }: { data: CreateReportType, patient_id: number }) => {
      const client = await graphqlClient()
      return await client.request({
        document: CREATE_REPORT,
        variables: { 
          ...data,
          patient_id,  
          collection_date: data.collection_date ? format(data.collection_date, 'yyyy-MM-dd HH:mm:ss') : undefined,
          test_date: data.test_date ? format(data.test_date, 'yyyy-MM-dd HH:mm:ss') : undefined,
          report_generation_date: data.report_generation_date ? format(data.report_generation_date, 'yyyy-MM-dd HH:mm:ss') : undefined,
        }
      })
    }
  })

  return { createReport }
}
