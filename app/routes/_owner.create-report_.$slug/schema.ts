import { z } from 'zod'
import { FieldInputType } from '~/gql/graphql'

// 1) Map the GraphQL enum to a Zod enum
export const FieldInputTypeEnum = z.nativeEnum(FieldInputType)

// 2) Recursive SubFieldInput schema (for nested sub_fields in FieldInput)
const SubFieldInputSchema: z.ZodType<{
  id?: number
  input_type: z.infer<typeof FieldInputTypeEnum>
  name: string
  name_value?: string
  unit?: string
  unit_value?: string
  unit_active?: boolean
  reference?: string
  reference_value?: string
  reference_active?: boolean
}> = z.lazy(() =>
  z.object({
    id: z.number().optional(),
    input_type: FieldInputTypeEnum,
    name: z.string(),
    name_value: z.string().optional(),
    unit: z.string().optional(),
    unit_value: z.string().optional(),
    unit_active: z.boolean().optional(),
    reference: z.string().optional(),
    reference_value: z.string().optional(),
    reference_active: z.boolean().optional(),
  }),
)

// 3) FieldInput schema
export const FieldInputSchema = z.object({
  id: z.number().optional(),
  input_type: FieldInputTypeEnum,
  input_type_values: z.array(z.string()).optional(),
  name: z.string(),
  name_value: z.string().optional(),
  reference: z.string().optional(),
  reference_value: z.string().optional(),
  sub_fields: z.array(SubFieldInputSchema).optional(),
  unit: z.string().optional(),
  unit_value: z.string().optional(),
})

// 4) SectionInput schema
export const SectionInputSchema = z.object({
  fields: z.array(FieldInputSchema),
  id: z.number().optional(),
  name: z.string(),
})

// 5) CreateReportSchema for the mutation variables
export const CreateReportSchema = z.object({
  doctor_id: z.number().optional(),
  template_id: z.number(),
  fields_input: z.array(FieldInputSchema).optional(),
  sections_input: z.array(SectionInputSchema).optional(),
  // patient_id: z.number().optional(), inserted as part of mutation
  remarks: z.string().optional(),
  staff: z.string().optional(),
  collection_date: z.string().optional(),
  test_date: z.string().optional(),
  report_generation_date: z.string().optional(),
})

export type CreateReportType = z.infer<typeof CreateReportSchema>
