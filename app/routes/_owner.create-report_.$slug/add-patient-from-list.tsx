import type { SelectedPatient } from '~/lib/types/selected-patient'
import { useState } from 'react'
import { TableLoader } from '~/components/common/common-loaders'
import PagePagination from '~/components/common/page-pagination'
import { But<PERSON> } from '~/components/ui/button'
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import useGetPatientList from '~/hooks/use-get-patient-list'

interface Props {
  onSelect: (patient: SelectedPatient) => void
}

export default function AddPatientFromList({ onSelect }: Props) {
  const { data, isLoading, isError, search, handleSearch, page, handlePageChange, totalPages } = useGetPatientList({ first: 10 })
  const [selectedPatient, setSelectedPatient] = useState<SelectedPatient | null>(null)
  const [isOpen, setIsOpen] = useState(false)

  const handleConfirm = () => {
    if (selectedPatient) {
      onSelect(selectedPatient)
      setSelectedPatient(null) // Reset selection
      setIsOpen(false) // Close dialog
      handleSearch('')
      handlePageChange(1)
    }
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open)
        setSelectedPatient(null)
        handleSearch('')
        handlePageChange(1)
      }}
    >
      <DialogTrigger asChild>
        <Button onClick={() => setIsOpen(true)}>
          Add Patient from list
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-240">
        <DialogHeader>
          <DialogTitle>Patient list</DialogTitle>
          <DialogDescription>
            Select a patient from the list below.
          </DialogDescription>
        </DialogHeader>
        <div className="mb-4">
          <Input
            value={search}
            onChange={(e) => {
              handleSearch(e.target.value)
              setSelectedPatient(null)
            }}
            placeholder="Search patient by name"
          />
        </div>
        {isLoading && (
          <div className="min-h-110 max-h-110 h-full">
            <TableLoader length={8} />
          </div>
        )}
        {isError && <p className="text-red-500">Error loading patients.</p>}
        {!isLoading && !isError && data?.getPatientList?.data && (
          <RadioGroup value={selectedPatient ? String(selectedPatient.id) : undefined}>
            <div className="min-h-110 h-full">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12"></TableHead>
                    <TableHead className="w-48">Name</TableHead>
                    <TableHead className="w-32">Phone Number</TableHead>
                    <TableHead className="w-64">Address</TableHead>
                    <TableHead className="w-16">Age</TableHead>
                    <TableHead className="w-20">Gender</TableHead>
                    <TableHead className="w-20">Blood Group</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.getPatientList.data.length > 0
                    ? (
                        data.getPatientList.data.map((patient) => {
                          return (
                            <TableRow
                              key={patient.id}
                              data-state={selectedPatient?.id === patient.id ? 'selected' : undefined}
                              className="cursor-pointer"
                              onClick={() => {
                                setSelectedPatient({
                                  ...patient,
                                })
                              }}
                            >
                              <TableCell>
                                <RadioGroupItem value={String(patient.id)} id={`patient-${patient.id}`} />
                              </TableCell>
                              <TableCell className="font-medium whitespace-normal ">{patient.name}</TableCell>
                              <TableCell>{patient.phone_number}</TableCell>
                              <TableCell className="whitespace-normal ">{patient.address}</TableCell>
                              <TableCell>{patient.age}</TableCell>
                              <TableCell>{patient.gender}</TableCell>
                              <TableCell>{patient.blood_group}</TableCell>
                            </TableRow>
                          )
                        })
                      )
                    : (
                        <TableRow>
                          <TableCell colSpan={4} className="h-24 text-center">
                            No patients found.
                          </TableCell>
                        </TableRow>
                      )}
                </TableBody>
              </Table>
            </div>
            {totalPages > 1 && (
              <PagePagination
                currentPage={page}
                handlePagePagination={handlePageChange}
                lastPage={totalPages}
              />
            )}
          </RadioGroup>
        )}
        <DialogFooter className="space-x-6">
          <Button variant="secondary" onClick={() => setIsOpen(false)}>Cancel</Button>
          <Button onClick={handleConfirm} disabled={!selectedPatient}>
            Confirm Selection
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
