import type { SelectedPatient } from '~/lib/types/selected-patient'
import { X } from 'lucide-react'
import { Button } from '~/components/ui/button'

interface Props {
  patient: SelectedPatient
  removePatient: () => void
}

export default function SelectedPatientCard({ patient, removePatient }: Props) {
  return (
    <div className="border p-4 rounded-lg w-full shadow relative bg-white">
      <div className="font-bold text-lg">Patient info</div>
      <div className="grid grid-cols-6 gap-2">
        <div className="col-span-1 flex flex-col">
          <span className="font-semibold">Name</span>
          <span>{patient.name}</span>
        </div>
        <div className="col-span-1 flex flex-col">
          <span className="font-semibold">Phone Number</span>
          <span>{patient.phone_number}</span>
        </div>
        <div className="col-span-1 flex flex-col">
          <span className="font-semibold">Age</span>
          {patient.age}
        </div>
        <div className="col-span-1 flex flex-col">
          <span className="font-semibold">Address</span>
          <span>{patient.address}</span>
        </div>
        <div className="col-span-1 flex flex-col">
          <span className="font-semibold">Gender</span>
          {patient.gender}
        </div>
        <div className="col-span-1 flex flex-col">
          <span className="font-semibold">Blood Group</span>
          {patient.blood_group}
        </div>
      </div>
      <div>
        <Button
          onClick={removePatient}
          variant="ghost"
          size="icon"
          className="absolute top-0 right-0"
        >
          <X />
        </Button>
      </div>
    </div>
  )
}
