import type { FieldInputType } from '~/gql/graphql'
import type { CreateTemplateType } from '~/routes/_owner.settings_.create-template/schema'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { X } from 'lucide-react'
import { HexColorPicker } from 'react-colorful'
import { toast } from 'sonner'
import BackButton from '~/components/common/back-button'
import CustomTooltip from '~/components/common/custom-tooltip'
import PageHeader from '~/components/common/page-header'
import { Button } from '~/components/ui/button'
import { Checkbox } from '~/components/ui/checkbox'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group'
import { Textarea } from '~/components/ui/textarea'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { cn } from '~/lib/utils'
import { CreateTemplateSchema } from '~/routes/_owner.settings_.create-template/schema'
import useCreateTemplate from '~/routes/_owner.settings_.create-template/use-create-template'

export default function CreateTemplate() {
  const { createTemplate } = useCreateTemplate()

  const queryClient = useQueryClient()

  const form = useForm({
    defaultValues: {
      title_color: '#000',
      report_name_color: '#000',
      name: '',
      sections_input: [],
      fields_input: [],
    } as CreateTemplateType,
    validators: {
      onSubmit: CreateTemplateSchema,
    },
    onSubmit: ({ value }) => {
      createTemplate.mutate(value, {
        onSuccess: () => {
          toast.success('Template created successfully')
          queryClient.invalidateQueries({
            queryKey: ['get-templates'],
          })
          form.reset()
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
        },
      })
    },
  })

  return (
    <div className="flex flex-col grow max-w-4xl mx-auto w-full">
      <div className="mb-4">
        <BackButton />
      </div>
      <PageHeader title="Create New Template" />
      <form
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
        className="mt-4 flex flex-col gap-4"
      >
        <div className="flex gap-x-4">
          <form.Field
            name="title_color"
            children={({ state, handleChange }) => (
              <FormItem className="flex flex-row gap-x-4 basis-1/2">
                <Popover>
                  <PopoverTrigger>
                    <CustomTooltip message="Pick title color">
                      <Button
                        id="title_color"
                        type="button"
                        style={{ backgroundColor: state.value || '#000' }}
                        size="icon"
                      />
                    </CustomTooltip>
                  </PopoverTrigger>
                  <PopoverContent>
                    <HexColorPicker
                      defaultValue="#000"
                      color={state.value || '#000'}
                      onChange={handleChange}
                    />
                  </PopoverContent>
                </Popover>
                <Label htmlFor="title_color">Title color</Label>
              </FormItem>
            )}
          />
          <form.Field
            name="report_name_color"
            children={({ state, handleChange }) => (
              <FormItem className="flex flex-row gap-x-4 basis-1/2">
                <Popover>
                  <PopoverTrigger>
                    <CustomTooltip message="Pick report name color">
                      <Button
                        id="report_name_color"
                        type="button"
                        style={{ backgroundColor: state.value || '#000' }}
                        size="icon"
                      />
                    </CustomTooltip>
                  </PopoverTrigger>
                  <PopoverContent>
                    <HexColorPicker
                      defaultValue="#000"
                      color={state.value || '#000'}
                      onChange={handleChange}
                    />
                  </PopoverContent>
                </Popover>
                <Label htmlFor="report_name_color">Report name color</Label>
              </FormItem>
            )}
          />
        </div>
        <form.Field
          name="name"
          children={({ state, handleBlur, handleChange }) => (
            <FormItem>
              <Label>Name</Label>
              <Input
                value={state.value}
                onBlur={handleBlur}
                onChange={e => handleChange(e.target.value)}
                placeholder="Template Name"
              />
              <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
            </FormItem>
          )}
        />
        <form.Field name="sections_input" mode="array">
          {sectionsField => (
            <>
              {sectionsField.state.value && sectionsField.state.value.length > 0 && (
                <div className="flex flex-col gap-4">
                  {sectionsField.state.value?.map((_, i) => (
                    <div
                      key={i}
                      className="border p-4 rounded-lg shadow-md relative focus-within:ring-2 focus-within:ring-blue-500"
                    >
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute top-0 right-0"
                        onClick={() => sectionsField.removeValue(i)}
                      >
                        <X />
                      </Button>
                      <form.Field name={`sections_input[${i}].name`}>
                        {(subField) => {
                          return (
                            <FormItem>
                              <Label>Section name</Label>
                              <Input
                                value={subField.state.value}
                                onChange={e => subField.handleChange(e.target.value)}
                                placeholder="Section name"
                                autoFocus
                              />
                              <FormMessage
                                errors={subField.state.meta.errors
                                  .map(e => e?.message)
                                  .join(', ')}
                              />
                            </FormItem>
                          )
                        }}
                      </form.Field>
                      <form.Field name={`sections_input[${i}].fields`} mode="array">
                        {fieldsSubField => (
                          <div
                            className={cn('', {
                              'border-t mt-4':
                                fieldsSubField.state.value && fieldsSubField.state.value.length > 0,
                            })}
                          >
                            {fieldsSubField.state.value?.map((_, j) => (
                              <div
                                key={j}
                                className="flex gap-4 border p-4 rounded-lg shadow-md mt-4 flex-wrap shrink-0 relative focus-within:ring-2 focus-within:ring-green-500"
                              >
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  className="absolute top-0 right-0"
                                  onClick={() => fieldsSubField.removeValue(j)}
                                >
                                  <X />
                                </Button>
                                <form.Field name={`sections_input[${i}].fields[${j}].input_type`}>
                                  {subField => (
                                    <>
                                      <FormItem className="basis-full">
                                        <Label>Input type</Label>
                                        <RadioGroup
                                          className="flex justify-between"
                                          value={subField.state.value}
                                          onValueChange={(e) => {
                                            subField.handleChange(e as FieldInputType)
                                            form.setFieldValue(
                                              `sections_input[${i}].fields[${j}].sub_fields`,
                                              [],
                                            )
                                            form.setFieldValue(
                                              `sections_input[${i}].fields[${j}].input_type_values`,
                                              [],
                                            )
                                          }}
                                        >
                                          <Label>
                                            <RadioGroupItem value="TEXTFIELD" />
                                            Text
                                          </Label>
                                          <Label>
                                            <RadioGroupItem value="RICHTEXT" />
                                            Richtext
                                          </Label>
                                          <Label>
                                            <RadioGroupItem value="SELECT" />
                                            Select
                                          </Label>
                                          <Label>
                                            <RadioGroupItem value="MULTISELECT" />
                                            Multiselect
                                          </Label>
                                          <Label>
                                            <RadioGroupItem value="SUBFIELD" />
                                            Subfield
                                          </Label>
                                        </RadioGroup>
                                      </FormItem>
                                      <form.Field name={`sections_input[${i}].fields[${j}].name`}>
                                        {nSubField => (
                                          <FormItem className="basis-[calc(25%-0.75rem)]">
                                            <Label>Test name</Label>
                                            <Input
                                              value={nSubField.state.value}
                                              onChange={e =>
                                                nSubField.handleChange(e.target.value)}
                                              placeholder="Field name"
                                              autoFocus
                                            />
                                            <FormMessage
                                              errors={nSubField.state.meta.errors
                                                .map(e => e?.message)
                                                .join(', ')}
                                            />
                                          </FormItem>
                                        )}
                                      </form.Field>
                                      <FormItem className="basis-[calc(25%-0.75rem)]">
                                        <Label>Result</Label>
                                        <Input disabled placeholder="Result" />
                                      </FormItem>
                                      <form.Field
                                        name={`sections_input[${i}].fields[${j}].unit_active`}
                                      >
                                        {unitActiveSubField => (
                                          <FormItem className="basis-[calc(25%-0.75rem)]">
                                            <div className="flex items-center gap-x-2">
                                              <Checkbox
                                                id={`unit-${i}-${j}`}
                                                checked={unitActiveSubField.state.value === true}
                                                onCheckedChange={checked =>
                                                  unitActiveSubField.handleChange(!!checked)}
                                              />
                                              <Label htmlFor={`unit-${i}-${j}`}>Unit</Label>
                                            </div>
                                            <form.Field
                                              name={`sections_input[${i}].fields[${j}].unit`}
                                            >
                                              {unitSubField => (
                                                <Input
                                                  value={unitSubField.state.value}
                                                  onChange={e =>
                                                    unitSubField.handleChange(e.target.value)}
                                                  placeholder="Unit"
                                                  disabled={unitActiveSubField.state.value !== true}
                                                />
                                              )}
                                            </form.Field>
                                          </FormItem>
                                        )}
                                      </form.Field>
                                      <form.Field
                                        name={`sections_input[${i}].fields[${j}].reference_active`}
                                      >
                                        {referenceActiveSubField => (
                                          <FormItem className="basis-[calc(25%-0.75rem)]">
                                            <div className="flex items-center gap-x-2">
                                              <Checkbox
                                                id={`reference-${i}-${j}`}
                                                checked={
                                                  referenceActiveSubField.state.value === true
                                                }
                                                onCheckedChange={checked =>
                                                  referenceActiveSubField.handleChange(!!checked)}
                                              />
                                              <Label htmlFor={`reference-${i}-${j}`}>
                                                BIO.REF.INTERVAL
                                              </Label>
                                            </div>
                                            <form.Field
                                              name={`sections_input[${i}].fields[${j}].reference`}
                                            >
                                              {referenceSubField => (
                                                <Textarea
                                                  value={referenceSubField.state.value}
                                                  onChange={e =>
                                                    referenceSubField.handleChange(e.target.value)}
                                                  placeholder="BIO.REF.INTERVALS"
                                                  disabled={
                                                    referenceActiveSubField.state.value !== true
                                                  }
                                                />
                                              )}
                                            </form.Field>
                                          </FormItem>
                                        )}
                                      </form.Field>
                                      {subField.state.value === 'SUBFIELD' && (
                                        <>
                                          <form.Field
                                            name={`sections_input[${i}].fields[${j}].sub_fields`}
                                            mode="array"
                                          >
                                            {subFieldsField => (
                                              <>
                                                {subFieldsField.state.value
                                                  && subFieldsField.state.value.length > 0
                                                  ? (
                                                      <div className="flex flex-col gap-4 basis-full border p-4 rounded-lg shadow-md focus-within:ring-2 focus-within:ring-amber-500">
                                                        {subFieldsField.state.value?.map((_, k) => (
                                                          <div
                                                            key={k}
                                                            className="flex size-full items-center gap-x-4"
                                                          >
                                                            <form.Field
                                                              name={`sections_input[${i}].fields[${j}].sub_fields[${k}].name`}
                                                            >
                                                              {subField => (
                                                                <FormItem className="basis-[calc(33%-0.75rem)]">
                                                                  <Label>Subfield name</Label>
                                                                  <Input
                                                                    value={subField.state.value}
                                                                    onChange={e =>
                                                                      subField.handleChange(
                                                                        e.target.value,
                                                                      )}
                                                                    placeholder="Subfield name"
                                                                    autoFocus
                                                                  />
                                                                </FormItem>
                                                              )}
                                                            </form.Field>
                                                            <form.Field
                                                              name={`sections_input[${i}].fields[${j}].sub_fields[${k}].unit_active`}
                                                            >
                                                              {subFieldUnitActiveSubField => (
                                                                <FormItem className="basis-[calc(33%-0.75rem)]">
                                                                  <div className="flex items-center gap-x-2">
                                                                    <Checkbox
                                                                      id={`unit-${i}-${j}-${k}`}
                                                                      checked={
                                                                        subFieldUnitActiveSubField.state
                                                                          .value === true
                                                                      }
                                                                      onCheckedChange={checked =>
                                                                        subFieldUnitActiveSubField.handleChange(
                                                                          !!checked,
                                                                        )}
                                                                    />
                                                                    <Label
                                                                      htmlFor={`unit-${i}-${j}-${k}`}
                                                                    >
                                                                      Unit
                                                                    </Label>
                                                                  </div>
                                                                  <form.Field
                                                                    name={`sections_input[${i}].fields[${j}].sub_fields[${k}].unit`}
                                                                  >
                                                                    {subFieldUnit => (
                                                                      <Input
                                                                        value={subFieldUnit.state.value}
                                                                        onChange={e =>
                                                                          subFieldUnit.handleChange(
                                                                            e.target.value,
                                                                          )}
                                                                        placeholder="Unit"
                                                                        disabled={
                                                                          subFieldUnitActiveSubField
                                                                            .state
                                                                            .value !== true
                                                                        }
                                                                      />
                                                                    )}
                                                                  </form.Field>
                                                                </FormItem>
                                                              )}
                                                            </form.Field>
                                                            <form.Field
                                                              name={`sections_input[${i}].fields[${j}].sub_fields[${k}].reference_active`}
                                                            >
                                                              {subFieldRefActiveSubField => (
                                                                <FormItem className="basis-[calc(33%-0.75rem)]">
                                                                  <div className="flex items-center gap-x-2">
                                                                    <Checkbox
                                                                      id={`ref-${i}-${j}-${k}`}
                                                                      checked={
                                                                        subFieldRefActiveSubField.state
                                                                          .value === true
                                                                      }
                                                                      onCheckedChange={checked =>
                                                                        subFieldRefActiveSubField.handleChange(
                                                                          !!checked,
                                                                        )}
                                                                    />
                                                                    <Label
                                                                      htmlFor={`ref-${i}-${j}-${k}`}
                                                                    >
                                                                      BIO.REF.INTERVAL
                                                                    </Label>
                                                                  </div>
                                                                  <form.Field
                                                                    name={`sections_input[${i}].fields[${j}].sub_fields[${k}].reference`}
                                                                  >
                                                                    {subFieldUnit => (
                                                                      <Textarea
                                                                        value={subFieldUnit.state.value}
                                                                        onChange={e =>
                                                                          subFieldUnit.handleChange(
                                                                            e.target.value,
                                                                          )}
                                                                        placeholder="BIO.REF.INTERVAL"
                                                                        disabled={
                                                                          subFieldRefActiveSubField
                                                                            .state
                                                                            .value !== true
                                                                        }
                                                                      />
                                                                    )}
                                                                  </form.Field>
                                                                </FormItem>
                                                              )}
                                                            </form.Field>
                                                            <Button
                                                              type="button"
                                                              size="icon"
                                                              variant="ghost"
                                                              onClick={() =>
                                                                subFieldsField.removeValue(k)}
                                                            >
                                                              <X />
                                                            </Button>
                                                          </div>
                                                        ))}
                                                      </div>
                                                    )
                                                  : (
                                                      <div className="basis-full" />
                                                    )}
                                                <Button
                                                  type="button"
                                                  variant="outline"
                                                  className="basis-full"
                                                  onClick={() => {
                                                    subFieldsField.pushValue({
                                                      name: '',
                                                      input_type: subField.state
                                                        .value as FieldInputType,
                                                    })
                                                  }}
                                                >
                                                  Add Subfield
                                                </Button>
                                              </>
                                            )}
                                          </form.Field>
                                        </>
                                      )}
                                      {(subField.state.value === 'SELECT'
                                        || subField.state.value === 'MULTISELECT') && (
                                        <>
                                          <form.Field
                                            name={`sections_input[${i}].fields[${j}].input_type_values`}
                                            mode="array"
                                          >
                                            {fieldsSubField => (
                                              <>
                                                {fieldsSubField.state.value
                                                  && fieldsSubField.state.value.length > 0
                                                  ? (
                                                      <div className="flex flex-col gap-4 basis-full border p-4 rounded-lg shadow-md focus-within:ring-2 focus-within:ring-amber-500">
                                                        {fieldsSubField.state.value?.map((_, k) => (
                                                          <div
                                                            key={k}
                                                            className="flex size-full items-center gap-x-4"
                                                          >
                                                            <form.Field
                                                              name={`sections_input[${i}].fields[${j}].input_type_values[${k}]`}
                                                            >
                                                              {optionField => (
                                                                <FormItem className="basis-full">
                                                                  <Label>Option value</Label>
                                                                  <Input
                                                                    value={optionField.state.value}
                                                                    onChange={e =>
                                                                      optionField.handleChange(
                                                                        e.target.value,
                                                                      )}
                                                                    placeholder="Option value"
                                                                    autoFocus
                                                                  />
                                                                </FormItem>
                                                              )}
                                                            </form.Field>
                                                            <Button
                                                              type="button"
                                                              size="icon"
                                                              variant="ghost"
                                                              onClick={() =>
                                                                fieldsSubField.removeValue(k)}
                                                            >
                                                              <X />
                                                            </Button>
                                                          </div>
                                                        ))}
                                                      </div>
                                                    )
                                                  : (
                                                      <div className="basis-full" />
                                                    )}
                                                <Button
                                                  className="basis-full"
                                                  variant="outline"
                                                  type="button"
                                                  onClick={() => {
                                                    fieldsSubField.pushValue('')
                                                  }}
                                                >
                                                  Add Option
                                                </Button>
                                              </>
                                            )}
                                          </form.Field>
                                        </>
                                      )}
                                    </>
                                  )}
                                </form.Field>
                              </div>
                            ))}
                            <Button
                              className="mt-4 w-full"
                              variant="outline"
                              type="button"
                              onClick={() => {
                                fieldsSubField.pushValue({
                                  name: '',
                                  input_type: 'TEXTFIELD' as FieldInputType,
                                  unit: '',
                                  reference: '',
                                  unit_active: false, // Add default value
                                  reference_active: false, // Add default value
                                  input_type_values: [],
                                  sub_fields: [],
                                })
                              }}
                            >
                              Add Test
                            </Button>
                          </div>
                        )}
                      </form.Field>
                    </div>
                  ))}
                </div>
              )}
              <Button
                type="button"
                variant="outline"
                onClick={() =>
                  sectionsField.pushValue({
                    name: '',
                    fields: [],
                  })}
              >
                Add Section
              </Button>
            </>
          )}
        </form.Field>

        <div className="flex justify-center">
          <Button isLoading={createTemplate.isPending} type="submit">
            Create New Template
          </Button>
        </div>
      </form>
    </div>
  )
}
