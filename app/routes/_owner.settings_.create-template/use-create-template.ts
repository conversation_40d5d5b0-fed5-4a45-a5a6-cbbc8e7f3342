import type { CreateTemplateType } from '~/routes/_owner.settings_.create-template/schema'
import { useMutation } from '@tanstack/react-query'
import { CREATE_TEMPLATE } from '~/graphql/mutations/create-template'
import { graphqlClient } from '~/lib/graphql-client'

export default function useCreateTemplate() {
  const createTemplate = useMutation({
    mutationFn: async (data: CreateTemplateType) => {
      const client = await graphqlClient()
      return await client.request({
        document: CREATE_TEMPLATE,
        variables: {
          ...data,
        },
      })
    },
  })

  return { createTemplate }
}
