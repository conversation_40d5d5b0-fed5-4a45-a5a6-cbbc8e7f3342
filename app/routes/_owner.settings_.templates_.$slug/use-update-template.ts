import type { UpdateTemplateType } from '~/routes/_owner.settings_.templates_.$slug/schema'
import { useMutation } from '@tanstack/react-query'
import { UPDATE_TEMPLATE } from '~/graphql/mutations/update-template'
import { graphqlClient } from '~/lib/graphql-client'

export default function useUpdateTemplate() {
  const updateTemplate = useMutation({
    mutationFn: async ({ data, id }: { data: UpdateTemplateType, id: number }) => {
      const client = await graphqlClient()
      return await client.request({
        document: UPDATE_TEMPLATE,
        variables: {
          ...data,
          id,
        },
      })
    },
  })

  return { updateTemplate }
}
