import type { UpdateTemplateType } from './schema'
import { Button } from '~/components/ui/button'
import { withForm } from '~/hooks/form'
import { SectionInput } from './section-input'

export const UpdateSectionsForm = withForm({
  defaultValues: {} as UpdateTemplateType,
  render: ({ form }) => (
    <form.Field name="sections_input" mode="array">
      {sectionsField => (
        <>
          {sectionsField.state.value && sectionsField.state.value.length > 0 && (
            <div className="flex flex-col gap-4">
              {sectionsField.state.value?.map((s, i) => (
                <SectionInput
                  key={s.id}
                  form={form}
                  sectionIndex={i}
                  onRemove={() => sectionsField.removeValue(i)}
                />
              ))}
            </div>
          )}
          <Button
            type="button"
            onClick={() => sectionsField.pushValue({
              name: '',
              fields: [],
            })}
            variant="outline"
          >
            Add Section
          </Button>
        </>
      )}
    </form.Field>
  ),

})
