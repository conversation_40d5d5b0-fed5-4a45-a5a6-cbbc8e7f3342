import type { UpdateTemplateType } from './schema'
import type { FieldInputType } from '~/gql/graphql'
import { X } from 'lucide-react'
import { memo } from 'react'
import { Button } from '~/components/ui/button'
import { Checkbox } from '~/components/ui/checkbox'
import FormItem from '~/components/ui/form-item'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group'
import { Textarea } from '~/components/ui/textarea'
import { withForm } from '~/hooks/form'
import { OptionsArray } from './options-array'
import { SubFieldsArray } from './sub-fields-array'

export const FieldInput = memo(withForm({
  defaultValues: {} as UpdateTemplateType,
  props: {
    sectionIndex: 0,
    fieldIndex: 0,
    onRemove: () => {},
  },
  render: ({ form, sectionIndex, fieldIndex, onRemove }) => {
    return (
      <div className="flex gap-4 border p-4 rounded-lg shadow-md mt-4 flex-wrap shrink-0 relative focus-within:ring-2 focus-within:ring-green-500">
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="absolute top-0 right-0"
          onClick={onRemove}
        >
          <X />
        </Button>

        <form.Field name={`sections_input[${sectionIndex}].fields[${fieldIndex}].input_type`}>
          {inputTypeField => (
            <>
              <FormItem className="basis-full">
                <Label>Input type</Label>
                <RadioGroup
                  className="flex justify-between"
                  value={inputTypeField.state.value}
                  onValueChange={(e) => {
                    inputTypeField.handleChange(e as FieldInputType)
                    form.setFieldValue(`sections_input[${sectionIndex}].fields[${fieldIndex}].sub_fields`, [])
                    form.setFieldValue(`sections_input[${sectionIndex}].fields[${fieldIndex}].input_type_values`, [])
                  }}
                >
                  <Label>
                    <RadioGroupItem value="TEXTFIELD" />
                    Text
                  </Label>
                  <Label>
                    <RadioGroupItem value="RICHTEXT" />
                    Richtext
                  </Label>
                  <Label>
                    <RadioGroupItem value="SELECT" />
                    Select
                  </Label>
                  <Label>
                    <RadioGroupItem value="MULTISELECT" />
                    Multiselect
                  </Label>
                  <Label>
                    <RadioGroupItem value="SUBFIELD" />
                    Subfield
                  </Label>
                </RadioGroup>
              </FormItem>

              <form.Field name={`sections_input[${sectionIndex}].fields[${fieldIndex}].name`}>
                {nameField => (
                  <FormItem className="basis-[calc(25%-0.75rem)]">
                    <Label>Test name</Label>
                    <Input
                      value={nameField.state.value}
                      onChange={e => nameField.handleChange(e.target.value)}
                      placeholder="Field name"
                      autoFocus
                    />
                  </FormItem>
                )}
              </form.Field>

              <FormItem className="basis-[calc(25%-0.75rem)]">
                <Label>Result</Label>
                <Input disabled placeholder="" />
              </FormItem>

              <form.Field name={`sections_input[${sectionIndex}].fields[${fieldIndex}].unit_active`}>
                {unitActiveSubField => (
                  <FormItem className="basis-[calc(25%-0.75rem)]">
                    <div className="flex items-center gap-x-2">
                      <Checkbox
                        id={`unit-${sectionIndex}-${fieldIndex}`}
                        checked={unitActiveSubField.state.value === true}
                        onCheckedChange={checked => unitActiveSubField.handleChange(!!checked)}
                      />
                      <Label htmlFor={`unit-${sectionIndex}-${fieldIndex}`}>Unit</Label>
                    </div>
                    <form.Field name={`sections_input[${sectionIndex}].fields[${fieldIndex}].unit`}>
                      {unitSubField => (
                        <Input
                          value={unitSubField.state.value}
                          onChange={e => unitSubField.handleChange(e.target.value)}
                          placeholder="Unit"
                          disabled={unitActiveSubField.state.value !== true}
                        />
                      )}
                    </form.Field>
                  </FormItem>
                )}
              </form.Field>
              <form.Field name={`sections_input[${sectionIndex}].fields[${fieldIndex}].reference_active`}>
                {referenceActiveSubField => (
                  <FormItem className="basis-[calc(25%-0.75rem)]">
                    <div className="flex items-center gap-x-2">
                      <Checkbox
                        id={`reference-${sectionIndex}-${fieldIndex}`}
                        checked={referenceActiveSubField.state.value === true}
                        onCheckedChange={checked => referenceActiveSubField.handleChange(!!checked)}
                      />
                      <Label htmlFor={`reference-${sectionIndex}-${fieldIndex}`}>BIO.REF.INTERVAL</Label>
                    </div>
                    <form.Field name={`sections_input[${sectionIndex}].fields[${fieldIndex}].reference`}>
                      {referenceSubField => (
                        <Textarea
                          value={referenceSubField.state.value}
                          onChange={e => referenceSubField.handleChange(e.target.value)}
                          placeholder="BIO.REF.INTERVAL"
                          disabled={referenceActiveSubField.state.value !== true}
                        />
                      )}
                    </form.Field>
                  </FormItem>
                )}
              </form.Field>
              {/* <ReferenceField sectionIndex={sectionIndex} fieldIndex={fieldIndex} /> */}

              {inputTypeField.state.value === 'SUBFIELD' && (
                <SubFieldsArray form={form} sectionIndex={sectionIndex} fieldIndex={fieldIndex} />
              )}

              {(inputTypeField.state.value === 'SELECT' || inputTypeField.state.value === 'MULTISELECT') && (
                <OptionsArray form={form} sectionIndex={sectionIndex} fieldIndex={fieldIndex} />
              )}
            </>
          )}
        </form.Field>
      </div>
    )
  },
}))
