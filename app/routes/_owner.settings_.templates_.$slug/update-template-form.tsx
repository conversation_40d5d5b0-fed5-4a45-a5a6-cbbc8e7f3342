import type { GetTemplateBySlugQuery } from '~/gql/graphql'
import { formOptions } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { useAppForm } from '~/hooks/form'
import parseFieldInputType from '~/lib/parse-field-input-type'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UpdateTemplateSchema, type UpdateTemplateType } from './schema'
import { SectionsArray } from './sections-array'
import { UpdateFormColors } from './update-form-colors'
import useUpdateTemplate from './use-update-template'

interface Props {
  templateData: GetTemplateBySlugQuery
}

export default function UpdateTemplateForm({ templateData }: Props) {
  const queryClient = useQueryClient()
  const { updateTemplate } = useUpdateTemplate()

  const sectionsInput
    = templateData.getTemplateBySlug.template_data.sections?.map(section => ({
      id: crypto.randomUUID(),
      name: section.name,
      fields:
        section.fields?.map(field => ({
          id: crypto.randomUUID(),
          input_type: parseFieldInputType(field.input_type ?? 'TEXTFIELD'),
          name: field.name ?? '',
          unit: field.unit ?? '',
          unit_active: field.unit_active ?? false,
          reference: field.reference ?? '',
          reference_active: field.reference_active ?? false,
          input_type_values: field.input_type_values?.map(val => val ?? '') ?? [],
          sub_fields:
            field.sub_fields?.map(subField => ({
              id: crypto.randomUUID(),
              input_type: parseFieldInputType(subField.input_type ?? 'TEXTFIELD'),
              name: subField.name ?? '',
              unit: subField.unit ?? '',
              unit_active: subField.unit_active ?? false,
              reference: subField.reference ?? '',
              reference_active: subField.reference_active ?? false,
            })) ?? [],
        })) ?? [],
    })) ?? []

  const formOpts = formOptions({
    defaultValues: {
      title_color: templateData.getTemplateBySlug.title_color ?? '#000',
      report_name_color: templateData.getTemplateBySlug.report_name_color ?? '#000',
      name: templateData.getTemplateBySlug.name,
      sections_input: sectionsInput,
      fields_input: [],
    } as UpdateTemplateType, // We might need a new type or adjust this one for updates
  })

  const form = useAppForm({
    ...formOpts,
    validators: {
      onSubmit: UpdateTemplateSchema, // This schema might need adjustment for updates
    },
    onSubmit: ({ value }) => {
      // if (!data?.getTemplateBySlug?.id) {
      //   toast.error('Template not found')
      //   return
      // }
      updateTemplate.mutate(
        {
          data: value,
          id: templateData.getTemplateBySlug.id,
        },
        {
          onSuccess: () => {
            toast.success('Template updated successfully')
            queryClient.invalidateQueries({ queryKey: ['get-template-by-slug'] })
          },
          onError: (error: Error) => {
            toast.error(parseGraphqlError(error))
          },
        },
      )
    },
  })

  return (
    <form
      onSubmit={async (e) => {
        e.preventDefault()
        e.stopPropagation()
        form.handleSubmit()
      }}
      className="mt-4 flex flex-col gap-4"
    >
      {/* Color section */}
      <UpdateFormColors form={form} />

      <form.Field
        name="name"
        children={({ state, handleBlur, handleChange }) => (
          <FormItem>
            <Label>Name</Label>
            <Input
              value={state.value}
              onBlur={handleBlur}
              onChange={e => handleChange(e.target.value)}
              placeholder="Template Name"
            />
            <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
          </FormItem>
        )}
      />

      <SectionsArray form={form} />

      <div className="flex justify-center">
        <Button isLoading={updateTemplate.isPending} type="submit">
          Update Template
        </Button>
      </div>
    </form>
  )
}
