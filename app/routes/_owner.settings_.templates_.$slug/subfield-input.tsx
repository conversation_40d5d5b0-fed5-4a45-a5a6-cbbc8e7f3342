import type { UpdateTemplateType } from './schema'
import { X } from 'lucide-react'
import { memo } from 'react'
import { Button } from '~/components/ui/button'
import { Checkbox } from '~/components/ui/checkbox'
import FormItem from '~/components/ui/form-item'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Textarea } from '~/components/ui/textarea'
import { withForm } from '~/hooks/form'

export const SubFieldInput = memo(
  withForm({
    defaultValues: {} as UpdateTemplateType,
    props: {
      sectionIndex: 0,
      fieldIndex: 0,
      subFieldIndex: 0,
      onRemove: () => {},
    },
    render: ({ form, sectionIndex, fieldIndex, subFieldIndex, onRemove }) => {
      return (
        <div className="flex size-full items-center gap-x-4">
          <form.Field
            name={`sections_input[${sectionIndex}].fields[${fieldIndex}].sub_fields[${subFieldIndex}].name`}
          >
            {field => (
              <FormItem className="basis-[calc(33%-0.75rem)]">
                <Label>Subfield name</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Subfield name"
                  autoFocus
                />
              </FormItem>
            )}
          </form.Field>

          <form.Field
            name={`sections_input[${sectionIndex}].fields[${fieldIndex}].sub_fields[${subFieldIndex}].unit_active`}
          >
            {unitActiveField => (
              <FormItem className="basis-[calc(33%-0.75rem)]">
                <div className="flex items-center gap-x-2">
                  <Checkbox
                    id={`unit-${sectionIndex}-${fieldIndex}-${subFieldIndex}`}
                    checked={unitActiveField.state.value === true}
                    onCheckedChange={checked => unitActiveField.handleChange(!!checked)}
                  />
                  <Label htmlFor={`unit-${sectionIndex}-${fieldIndex}-${subFieldIndex}`}>
                    Unit
                  </Label>
                </div>
                <form.Field
                  name={`sections_input[${sectionIndex}].fields[${fieldIndex}].sub_fields[${subFieldIndex}].unit`}
                >
                  {unitField => (
                    <Input
                      value={unitField.state.value}
                      onChange={e => unitField.handleChange(e.target.value)}
                      placeholder="Unit"
                      disabled={unitActiveField.state.value !== true}
                    />
                  )}
                </form.Field>
              </FormItem>
            )}
          </form.Field>

          <form.Field
            name={`sections_input[${sectionIndex}].fields[${fieldIndex}].sub_fields[${subFieldIndex}].reference_active`}
          >
            {refActiveField => (
              <FormItem className="basis-[calc(33%-0.75rem)]">
                <div className="flex items-center gap-x-2">
                  <Checkbox
                    id={`ref-${sectionIndex}-${fieldIndex}-${subFieldIndex}`}
                    checked={refActiveField.state.value === true}
                    onCheckedChange={checked => refActiveField.handleChange(!!checked)}
                  />
                  <Label htmlFor={`ref-${sectionIndex}-${fieldIndex}-${subFieldIndex}`}>
                    BIO.REF.INTERVAL
                  </Label>
                </div>
                <form.Field
                  name={`sections_input[${sectionIndex}].fields[${fieldIndex}].sub_fields[${subFieldIndex}].reference`}
                >
                  {refField => (
                    <Textarea
                      value={refField.state.value}
                      onChange={e => refField.handleChange(e.target.value)}
                      placeholder="BIO.REF.INTERVAL"
                      disabled={refActiveField.state.value !== true}
                    />
                  )}
                </form.Field>
              </FormItem>
            )}
          </form.Field>

          <Button type="button" size="icon" variant="ghost" onClick={onRemove}>
            <X />
          </Button>
        </div>
      )
    },
  }),
)
