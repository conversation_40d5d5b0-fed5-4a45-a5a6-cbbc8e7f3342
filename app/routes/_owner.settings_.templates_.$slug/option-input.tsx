import type { UpdateTemplateType } from './schema'
import { X } from 'lucide-react'
import { memo } from 'react'
import { Button } from '~/components/ui/button'
import FormItem from '~/components/ui/form-item'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { withForm } from '~/hooks/form'

export const OptionInput = memo(withForm({
  defaultValues: {} as UpdateTemplateType,
  props: {
    sectionIndex: 0,
    fieldIndex: 0,
    optionIndex: 0,
    onRemove: () => {},
  },
  render: ({ form, sectionIndex, fieldIndex, optionIndex, onRemove }) => {
    return (
      <div className="flex size-full items-center gap-x-4">
        <form.Field name={`sections_input[${sectionIndex}].fields[${fieldIndex}].input_type_values[${optionIndex}]`}>
          {(field: any) => (
            <FormItem className="basis-full">
              <Label>Option value</Label>
              <Input
                value={field.state.value}
                onChange={e => field.handleChange(e.target.value)}
                placeholder="Option value"
                autoFocus
              />
            </FormItem>
          )}
        </form.Field>
        <Button
          type="button"
          size="icon"
          variant="ghost"
          onClick={onRemove}
        >
          <X />
        </Button>
      </div>
    )
  },
}))
