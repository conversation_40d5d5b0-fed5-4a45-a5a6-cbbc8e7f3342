import { useMutation } from '@tanstack/react-query'
import { DELETE_TEMPLATE } from '~/graphql/mutations/delete-template'
import { graphqlClient } from '~/lib/graphql-client'

export default function useDeleteTemplate() {
  const deleteTemplate = useMutation({
    mutationFn: async (id: number) => {
      const client = await graphqlClient()
      return await client.request({
        document: DELETE_TEMPLATE,
        variables: {
          id,
        },
      })
    },
  })

  return { deleteTemplate }
}
