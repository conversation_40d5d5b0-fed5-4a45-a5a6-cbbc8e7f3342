import type { UpdateTemplateType } from './schema'
import type { FieldInputType } from '~/gql/graphql'
import { X } from 'lucide-react'
import { memo } from 'react'
import { Button } from '~/components/ui/button'
import FormItem from '~/components/ui/form-item'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { withForm } from '~/hooks/form'
import { cn } from '~/lib/utils'
import { FieldInput } from './field-input'

export const SectionInput = memo(withForm({
  defaultValues: {} as UpdateTemplateType,
  props: {
    sectionIndex: 0,
    onRemove: () => {},
  },
  render: ({ form, sectionIndex, onRemove }) => {
    return (
      <div className="border p-4 rounded-lg shadow-md relative focus-within:ring-2 focus-within:ring-blue-500">
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="absolute top-0 right-0"
          onClick={onRemove}
        >
          <X />
        </Button>

        <form.Field name={`sections_input[${sectionIndex}].name`}>
          {nameField => (
            <FormItem>
              <Label>Section name</Label>
              <Input
                value={nameField.state.value}
                onChange={e => nameField.handleChange(e.target.value)}
                placeholder="Section name"
                autoFocus
              />
            </FormItem>
          )}
        </form.Field>

        <form.Field name={`sections_input[${sectionIndex}].fields`} mode="array">
          {fieldsField => (
            <div className={cn('', {
              'border-t mt-4': fieldsField.state.value && fieldsField.state.value.length > 0,
            })}
            >
              {fieldsField.state.value?.map((s, j) => (
                <FieldInput
                  key={s.id}
                  form={form}
                  sectionIndex={sectionIndex}
                  fieldIndex={j}
                  onRemove={() => fieldsField.removeValue(j)}
                />
              ))}
              <Button
                className="mt-4 w-full"
                variant="outline"
                type="button"
                onClick={() => {
                  fieldsField.pushValue({
                    name: '',
                    input_type: 'TEXTFIELD' as FieldInputType,
                    unit: '',
                    reference: '',
                    unit_active: false,
                    reference_active: false,
                    input_type_values: [],
                    sub_fields: [],
                  })
                }}
              >
                Add Test
              </Button>
            </div>
          )}
        </form.Field>
      </div>
    )
  },
}))
