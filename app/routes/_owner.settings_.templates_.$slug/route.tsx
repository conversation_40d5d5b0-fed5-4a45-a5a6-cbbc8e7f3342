import { useQueryClient } from '@tanstack/react-query'
import { useNavigate, useParams } from 'react-router'
import { toast } from 'sonner'
import BackButton from '~/components/common/back-button'
import CommonError from '~/components/common/common-error'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import PageHeader from '~/components/common/page-header'
import LoaderIcon from '~/components/icons/loader-icon'
import { Button } from '~/components/ui/button'
import useDialogStates from '~/hooks/use-dialog-states'
import useGetTemplateBySlug from '~/hooks/use-get-template-by-slug'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import useDeleteTemplate from '~/routes/_owner.settings_.templates_.$slug/use-delete-template'
import UpdateTemplateForm from './update-template-form'

export default function UpdateTemplate() {
  const navigate = useNavigate()
  const { slug } = useParams()
  const queryClient = useQueryClient()
  const { data, isLoading, isError } = useGetTemplateBySlug({ slug })
  const { deleteTemplate } = useDeleteTemplate()

  const { open, toggleDialog } = useDialogStates()

  const handleDeleteTemplate = () => {
    if (data?.getTemplateBySlug?.id) {
      deleteTemplate.mutate(data.getTemplateBySlug.id, {
        onSuccess: () => {
          toast.success('Template deleted successfully')
          queryClient.invalidateQueries({ queryKey: ['get-templates'] })
          navigate('/settings')
        },
        onError: (error: Error) => {
          toast.error(parseGraphqlError(error))
        },
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center grow">
        <LoaderIcon className="size-16 animate-spin" />
      </div>
    )
  }

  if (isError || !data?.getTemplateBySlug) {
    return <CommonError />
  }

  return (
    <>
      <div className="flex flex-col grow max-w-4xl mx-auto w-full">
        <div className="flex justify-between">
          <BackButton />
          <Button variant="destructive" onClick={() => toggleDialog(true)}>
            Delete Template
          </Button>
          <ConfirmationDialog
            open={open}
            handleOpenChange={toggleDialog}
            isPending={deleteTemplate.isPending}
            handleConfirm={handleDeleteTemplate}
          />
        </div>
        <PageHeader title="Update Template" />
        {data.getTemplateBySlug
          ? (
              <UpdateTemplateForm templateData={data} />
            )
          : null}
      </div>
    </>
  )
}
