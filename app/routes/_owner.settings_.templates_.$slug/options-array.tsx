import type { UpdateTemplateType } from './schema'
import { memo } from 'react'
import { Button } from '~/components/ui/button'
import { withForm } from '~/hooks/form'
import { OptionInput } from './option-input'

export const OptionsArray = memo(withForm({
  defaultValues: {} as UpdateTemplateType,
  props: {
    sectionIndex: 0,
    fieldIndex: 0,
  },
  render: ({ form, sectionIndex, fieldIndex }) => {
    return (
      <form.Field name={`sections_input[${sectionIndex}].fields[${fieldIndex}].input_type_values`} mode="array">
        {optionsField => (
          <>
            {optionsField.state.value && optionsField.state.value.length > 0 && (
              <div className="flex flex-col gap-4 basis-full border p-4 rounded-lg shadow-md focus-within:ring-2 focus-within:ring-amber-500">
                {optionsField.state.value?.map((s, k) => (
                  <OptionInput
                    key={s}
                    form={form}
                    sectionIndex={sectionIndex}
                    fieldIndex={fieldIndex}
                    optionIndex={k}
                    onRemove={() => optionsField.removeValue(k)}
                  />
                ))}
              </div>
            )}
            <Button
              type="button"
              className="basis-full"
              variant="outline"
              onClick={() => optionsField.pushValue('')}
            >
              Add Option
            </Button>
          </>
        )}
      </form.Field>
    )
  },
}))
