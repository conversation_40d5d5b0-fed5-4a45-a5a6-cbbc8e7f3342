import type { UpdateTemplateType } from './schema'
import type { FieldInputType } from '~/gql/graphql'
import { memo } from 'react'
import { Button } from '~/components/ui/button'
import { withForm } from '~/hooks/form'
import { SubFieldInput } from './subfield-input'

export const SubFieldsArray = memo(withForm({
  defaultValues: {} as UpdateTemplateType,
  props: {
    sectionIndex: 0,
    fieldIndex: 0,
  },
  render: ({ form, sectionIndex, fieldIndex }) => {
    return (
      <form.Field name={`sections_input[${sectionIndex}].fields[${fieldIndex}].sub_fields`} mode="array">
        {subFieldsField => (
          <>
            {subFieldsField.state.value && subFieldsField.state.value.length > 0 && (
              <div className="flex flex-col gap-4 basis-full border p-4 rounded-lg shadow-md focus-within:ring-2 focus-within:ring-amber-500">
                {subFieldsField.state.value?.map((s, k) => (
                  <SubFieldInput
                    key={s.id}
                    form={form}
                    sectionIndex={sectionIndex}
                    fieldIndex={fieldIndex}
                    subFieldIndex={k}
                    onRemove={() => subFieldsField.removeValue(k)}
                  />
                ))}
              </div>
            )}
            <Button
              className="basis-full"
              variant="outline"
              type="button"
              onClick={() => {
                subFieldsField.pushValue({
                  name: '',
                  input_type: 'TEXTFIELD' as FieldInputType,
                  unit: '',
                  unit_active: false,
                  reference: '',
                  reference_active: false,
                })
              }}
            >
              Add Subfield
            </Button>
          </>
        )}
      </form.Field>
    )
  },
}))
