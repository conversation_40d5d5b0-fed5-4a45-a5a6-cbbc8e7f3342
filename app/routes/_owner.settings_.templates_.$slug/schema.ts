import { z } from 'zod'
import { FieldInputType } from '~/gql/graphql'

export const FieldInputTypeEnum = z.nativeEnum(FieldInputType)

// 2) Recursive SubFieldInput (for FieldInput.sub_fields)
const SubFieldInputSchema: z.ZodType<{
  children?: any[]
  id?: string
  input_type: z.infer<typeof FieldInputTypeEnum>
  name: string
  unit?: string
  unit_active?: boolean
  reference?: string
  reference_active?: boolean
}> = z.lazy(() =>
  z.object({
    // children: z.array(SubFieldInputSchema).optional(),
    id: z.string().optional(),
    input_type: FieldInputTypeEnum,
    name: z.string(),
    unit: z.string().optional(),
    unit_active: z.boolean().optional(),
    reference: z.string().optional(),
    reference_active: z.boolean().optional(),
  }),
)

// 3) FieldInput
export const FieldInputSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  input_type: FieldInputTypeEnum,
  input_type_values: z.array(z.string()).optional(),
  reference: z.string().optional(),
  reference_active: z.boolean().optional(),
  sub_fields: z.array(SubFieldInputSchema).optional(),
  unit: z.string().optional(),
  unit_active: z.boolean().optional(),
})

// 4) SectionInput
export const SectionInputSchema = z.object({
  fields: z.array(FieldInputSchema), // NON_NULL [FieldInput!]!
  id: z.string().optional(),
  name: z.string(),
})

// 5) The CreateTemplate “variables” schema
export const UpdateTemplateSchema = z.object({
  title_color: z.string().optional(),
  report_name_color: z.string().optional(),
  fields_input: z.array(FieldInputSchema).optional(), // NON_NULL [FieldInput!]!
  name: z.string().optional(), // NON_NULL String!
  sections_input: z.array(SectionInputSchema).optional(), // [SectionInput!]
})

export type UpdateTemplateType = z.infer<typeof UpdateTemplateSchema>
