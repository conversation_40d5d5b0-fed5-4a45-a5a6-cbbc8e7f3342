import type { SelectedPatient } from '~/lib/types/selected-patient'
import type { UpdatePatientType } from '~/lib/types/update-patient-schema'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import useUpdatePatient from '~/hooks/use-update-patient'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UpdatePatientSchema } from '~/lib/types/update-patient-schema'

interface Props {
  patient: SelectedPatient
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function UpdatePatientDialog({ patient, open, onOpenChange }: Props) {
  const queryClient = useQueryClient()
  const { updatePatient } = useUpdatePatient()

  const form = useForm({
    defaultValues: {
      name: patient.name,
      age: patient.age,
      gender: patient.gender,
      blood_group: patient.blood_group,
      address: patient.address,
      phone_number: patient.phone_number,
    } as UpdatePatientType,
    validators: {
      onSubmit: UpdatePatientSchema,
    },
    onSubmit: ({ value }) => {
      updatePatient.mutate({ id: patient.id, data: value }, {
        onSuccess: () => {
          toast.success('Patient updated successfully')
          queryClient.invalidateQueries({ queryKey: ['patient-list'] })
          form.reset()
          onOpenChange(false)
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
        },
      })
    },
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="min-w-2xl w-full">
        <DialogHeader>
          <DialogTitle>
            Update Patient
          </DialogTitle>
          <DialogDescription>
            Update patient details
          </DialogDescription>
        </DialogHeader>
        <form
          className="flex flex-col gap-4"
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
        >
          <form.Field
            name="name"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Name</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Patient Name"
                />
                <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          />
          <form.Field
            name="phone_number"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Phone number</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Phone number"
                  maxLength={10}
                />
                <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          />
          <form.Field
            name="address"
            children={({ state, handleBlur, handleChange }) => (
              <FormItem>
                <Label>Address</Label>
                <Input
                  value={state.value}
                  onBlur={handleBlur}
                  onChange={e => handleChange(e.target.value)}
                  placeholder="Address"
                />
                <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          />
          <div className="flex gap-4">
            <form.Field
              name="age"
              children={({ state, handleBlur, handleChange }) => (
                <FormItem>
                  <Label>Age</Label>
                  <Input
                    value={state.value}
                    onBlur={handleBlur}
                    onChange={e => handleChange(Number(e.target.value))}
                    placeholder="Age"
                  />
                  <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
                </FormItem>
              )}
            />
            <form.Field
              name="gender"
              children={({ state, handleBlur, handleChange }) => (
                <FormItem>
                  <Label>Gender</Label>
                  <Input
                    value={state.value}
                    onBlur={handleBlur}
                    onChange={e => handleChange(e.target.value)}
                    placeholder="Gender"
                  />
                  <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
                </FormItem>
              )}
            />
            <form.Field
              name="blood_group"
              children={({ state, handleBlur, handleChange }) => (
                <FormItem>
                  <Label>Blood group</Label>
                  <Input
                    value={state.value}
                    onBlur={handleBlur}
                    onChange={e => handleChange(e.target.value)}
                    placeholder="Blood group"
                  />
                  <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
                </FormItem>
              )}
            />

          </div>
          <DialogFooter className="gap-x-8">
            <Button type="button" variant="secondary" onClick={() => onOpenChange(false)}>Cancel</Button>
            <Button isLoading={updatePatient.isPending} type="submit">Update Patient</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
