import AddPatientDialog from '~/components/common/add-patient-dialog'
import PageHeader from '~/components/common/page-header'
import { Input } from '~/components/ui/input'
import useGetPatientList from '~/hooks/use-get-patient-list'
import PatientListTable from './patient-list-table'

export default function PatientList() {
  const { search, handleSearch } = useGetPatientList({})
  return (
    <>
      <div className="flex flex-col grow">
        <div className="flex justify-between items-center">
          <PageHeader title="Patient List" />
          <AddPatientDialog />
        </div>
        <div className="max-w-96 w-full my-4">
          <Input
            value={search}
            onChange={e => handleSearch(e.target.value)}
            placeholder="Search patient by name"
          />
        </div>
        <PatientListTable />
      </div>
    </>
  )
}
