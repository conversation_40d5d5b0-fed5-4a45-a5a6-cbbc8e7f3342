import type { SelectedPatient } from '~/lib/types/selected-patient'

import { useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { toast } from 'sonner'
import CommonError from '~/components/common/common-error'
import { TableLoader } from '~/components/common/common-loaders'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import CustomTooltip from '~/components/common/custom-tooltip'
import PagePagination from '~/components/common/page-pagination'
import DeleteIcon from '~/components/icons/delete-icon'
import UpdateIcon from '~/components/icons/update-icon'
import { Button } from '~/components/ui/button'
import { ScrollArea } from '~/components/ui/scroll-area'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import useDeletePatient from '~/hooks/use-delete-patient'
import useDialogStates from '~/hooks/use-dialog-states'
import useGetPatientList from '~/hooks/use-get-patient-list'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import UpdatePatientDialog from '~/routes/_owner.patient-list/update-patient-dialog'

export default function PatientListTable() {
  const [selectedPatient, setSelectedPatient]
    = useState<SelectedPatient | null>(null)
  const queryClient = useQueryClient()
  const { open: openDeleteDialog, toggleDialog: toggleDeleteDialog }
    = useDialogStates()
  const { open: openUpdateDialog, toggleDialog: toggleUpdateDialog }
    = useDialogStates()

  const {
    data,
    isLoading,
    isError,
    totalPages,
    handlePageChange,
    page,
  } = useGetPatientList({ first: 15 })
  const { deletePatient } = useDeletePatient()

  const handleDeletePatient = () => {
    if (!selectedPatient)
      return
    deletePatient.mutate(selectedPatient.id, {
      onSuccess: () => {
        toast.success('Patient deleted successfully')
        toggleDeleteDialog(false)
        queryClient.invalidateQueries({ queryKey: ['patient-list'] })
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  if (isError)
    return <CommonError message="Error fetching patient list" />

  return (
    <>
      <div className="flex flex-col grow">
        {
          data?.getPatientList?.data
          && data.getPatientList.data.length === 0
            ? (
                <div className="text-center font-bold text-xl">No patients found</div>
              )
            : (
                <ScrollArea className="w-full">
                  <Table className="min-w-320">
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[200px]">Name</TableHead>
                        <TableHead className="w-[150px]">Phone Number</TableHead>
                        <TableHead className="w-[100px]">Age</TableHead>
                        <TableHead className="w-[100px]">Gender</TableHead>
                        <TableHead className="w-[120px]">Blood Group</TableHead>
                        <TableHead className="w-[250px]">Address</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading
                        ? (
                            <TableRow>
                              <TableCell colSpan={7} className="text-center">
                                <TableLoader length={15} />
                              </TableCell>
                            </TableRow>
                          )
                        : data?.getPatientList.data?.map(patient => (
                          <TableRow key={patient.id}>
                            <TableCell>{patient.name}</TableCell>
                            <TableCell>{patient.phone_number}</TableCell>
                            <TableCell>{patient.age}</TableCell>
                            <TableCell>{patient.gender}</TableCell>
                            <TableCell>{patient.blood_group}</TableCell>
                            <TableCell>{patient.address}</TableCell>
                            <TableCell>
                              <div className="flex gap-x-2">
                                <CustomTooltip message="Update patient">
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => {
                                      setSelectedPatient(patient)
                                      toggleUpdateDialog(true)
                                    }}
                                  >
                                    <UpdateIcon />
                                  </Button>
                                </CustomTooltip>
                                <CustomTooltip message="Delete patient">
                                  <Button
                                    onClick={() => {
                                      setSelectedPatient(patient)
                                      toggleDeleteDialog(true)
                                    }}
                                    variant="outline"
                                    size="icon"
                                  >
                                    <DeleteIcon />
                                  </Button>
                                </CustomTooltip>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              )
        }
      </div>
      {totalPages > 1
        ? (
            <PagePagination
              currentPage={page}
              handlePagePagination={handlePageChange}
              lastPage={totalPages}
            />
          )
        : null}
      {selectedPatient && (
        <ConfirmationDialog
          handleConfirm={handleDeletePatient}
          handleOpenChange={toggleDeleteDialog}
          isPending={deletePatient.isPending}
          open={openDeleteDialog}
        />
      )}
      {selectedPatient && (
        <UpdatePatientDialog
          patient={selectedPatient}
          open={openUpdateDialog}
          onOpenChange={(open) => {
            if (!open) {
              setSelectedPatient(null)
            }
            toggleUpdateDialog(open)
          }}
        />
      )}
    </>
  )
}
