import type { UpdateEstablishmentType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { UPDATE_ESTABLISHMENT } from '~/graphql/mutations/update-establishment'
import { graphqlClient } from '~/lib/graphql-client'

export default function useUpdateEstablishment() {
  const updateEstablishment = useMutation({
    mutationFn: async ({ data, id }: { data: UpdateEstablishmentType, id: number }) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: UPDATE_ESTABLISHMENT,
        variables: {
          id,
          ...data,
        },
      })
    },
  })

  return { updateEstablishment }
}
