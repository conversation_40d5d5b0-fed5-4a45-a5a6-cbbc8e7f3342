import type { SelectedEstablishment, UpdateEstablishmentType } from './schema'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UpdateEstablishmentSchema } from './schema'
import useUpdateEstablishment from './use-update-establishment'

interface Props {
  establishment: SelectedEstablishment
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function UpdateEstablishmentDialog({ establishment, open, onOpenChange }: Props) {
  const queryClient = useQueryClient()
  const { updateEstablishment } = useUpdateEstablishment()

  const form = useForm({
    defaultValues: {
      establishment_name: establishment.name,
      establishment_address: establishment.address,
      establishment_phone_number: establishment.phone_number,
      subtitle: establishment.subtitle,
      logo: undefined,
    } as UpdateEstablishmentType,
    validators: {
      onSubmit: UpdateEstablishmentSchema,
    },
    onSubmit: ({ value }) => {
      updateEstablishment.mutate({ id: establishment.id, data: value }, {
        onSuccess: () => {
          toast.success('Establishment updated successfully')
          queryClient.invalidateQueries({ queryKey: ['establishment-list'] })
          form.reset()
          onOpenChange(false)
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
        },
      })
    },
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update establishment</DialogTitle>
          <DialogDescription>Enter update details</DialogDescription>
        </DialogHeader>
        <form
          className="flex flex-col gap-4"
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
        >
          <form.Field name="establishment_name">
            {field => (
              <FormItem>
                <Label>Name</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter establishment name"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="subtitle">
            {field => (
              <FormItem>
                <Label>Subtitle</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter subtitle"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="establishment_address">
            {field => (
              <FormItem>
                <Label>Address</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter establishment address"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="establishment_phone_number">
            {field => (
              <FormItem>
                <Label>Phone number</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter phone number"
                  maxLength={10}
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="logo">
            {field => (
              <FormItem>
                <Label>Logo</Label>
                <Input
                  onChange={(e) => {
                    if (e.target.files && e.target.files.length > 0) {
                      field.handleChange(e.target.files[0])
                    }
                    else {
                      field.handleChange(undefined)
                    }
                  }}
                  placeholder="Upload logo"
                  type="file"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <DialogFooter>
            <Button
              isLoading={updateEstablishment.isPending}
              type="submit"
            >
              Update
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
