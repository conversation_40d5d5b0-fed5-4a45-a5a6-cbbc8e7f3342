import type { Establishment, User } from '~/gql/graphql'
import { z } from 'zod'

export const CreateOwnerWithEstablishmentSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  confirm_password: z.string().min(1, 'Confirm password is required'),
  establishment_name: z.string().min(1, 'Establishment name is required'),
  establishment_phone_number: z.string().min(1, 'Establishment phone number is required'),
  establishment_address: z.string().optional(),
  wa_phone_number_id: z.string().optional(),
  logo: z.instanceof(File).optional(),
  subtitle: z.string().optional(),
}).refine(data => data.password === data.confirm_password, {
  message: 'Passwords do not match',
  path: ['confirm_password'],
})

export type CreateOwnerWithEstablishmentType = z.infer<typeof CreateOwnerWithEstablishmentSchema>

export const UpdateEstablishmentSchema = z.object({
  establishment_name: z.string().optional(),
  establishment_address: z.string().optional(),
  establishment_phone_number: z.string().optional(),
  // wa_phone_number_id: z.string().optional(),
  logo: z.instanceof(File).optional(),
  subtitle: z.string().optional(),
})

export type UpdateEstablishmentType = z.infer<typeof UpdateEstablishmentSchema>

export type SelectedOwner = Pick<User, 'id' | 'username'>
export type SelectedEstablishment = Pick<Establishment, 'id' | 'name' | 'address' | 'phone_number'>

export const UpdateOwnerSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  confirm_password: z.string().min(1, 'Confirm password is required'),
}).refine(data => data.password === data.confirm_password, {
  message: 'Passwords do not match',
  path: ['confirm_password'],
})

export type UpdateOwnerType = z.infer<typeof UpdateOwnerSchema>
