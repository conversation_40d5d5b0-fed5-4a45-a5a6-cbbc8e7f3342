import { Input } from '~/components/ui/input'
import useGetEstablishmentList from '~/hooks/use-get-establishment-list'

export default function EstablishmentQuery() {
  const { search, handleSearch } = useGetEstablishmentList()

  return (
    <div className="flex gap-x-4 my-4">
      <Input
        className="basis-full md:basis-1/5"
        value={search}
        onChange={e => handleSearch(e.target.value)}
        placeholder="Search by establishment name"
      />
    </div>
  )
}
