import type { UpdateOwnerType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { UPDATE_OWNER } from '~/graphql/mutations/update-owner'
import { graphqlClient } from '~/lib/graphql-client'

export default function useUpdateOwner() {
  const updateOwner = useMutation({
    mutationFn: async ({ data, id }: { data: UpdateOwnerType, id: number }) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: UPDATE_OWNER,
        variables: {
          id,
          ...data,
        },
      })
    },
  })
  return { updateOwner }
}
