import type { CreateOwnerWithEstablishmentType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { CREATE_OWNER_WITH_ESTABLISHMENT } from '~/graphql/mutations/create-owner-with-establishment'
import { graphqlClient } from '~/lib/graphql-client'

export default function useCreateOwnerWithEstablishment() {
  const createOwnerWithEstablishment = useMutation({
    mutationFn: async (data: CreateOwnerWithEstablishmentType) => {
      const graphql = await graphqlClient()
      return await graphql.request({
        document: CREATE_OWNER_WITH_ESTABLISHMENT,
        variables: {
          ...data,
        },
      })
    },
  })

  return { createOwnerWithEstablishment }
}
