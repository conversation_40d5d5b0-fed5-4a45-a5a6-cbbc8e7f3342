import type { SelectedEstablishment, SelectedOwner } from './schema'
import { useState } from 'react'
import CommonError from '~/components/common/common-error'
import { TableLoader } from '~/components/common/common-loaders'
import CustomTooltip from '~/components/common/custom-tooltip'
import PagePagination from '~/components/common/page-pagination'
import UpdateIcon from '~/components/icons/update-icon'
import UserUpdateIcon from '~/components/icons/user-update-icon'
import { Button } from '~/components/ui/button'
import { ScrollArea } from '~/components/ui/scroll-area'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import useDialogStates from '~/hooks/use-dialog-states'
import useGetEstablishmentList from '~/hooks/use-get-establishment-list'
import UpdateEstablishmentDialog from './update-establishment-dialog'
import UpdateOwnerDialog from './update-owner-dialog'

export default function EstablishmentListTable() {
  const [selectedOwner, setSelectedOwner] = useState<SelectedOwner | null>(
    null,
  )
  const [selectedEstablishment, setSelectedEstablishment]
    = useState<SelectedEstablishment | null>(null)

  const { open: openUpdateOwner, toggleDialog: toggleUpdateOwner }
    = useDialogStates()
  const {
    open: openUpdateEstablishment,
    toggleDialog: toggleUpdateEstablishment,
  } = useDialogStates()

  const { data, isLoading, isError, totalPages, handlePageChange, page }
    = useGetEstablishmentList()

  if (isError)
    return <CommonError message="Error fetching establishment list" />

  return (
    <>
      <div className="flex flex-col grow">
        {data?.getEstablishmentList?.data
          && data.getEstablishmentList.data.length === 0
          ? (
              <div className="text-center font-bold text-xl">
                No establishments found
              </div>
            )
          : (
              <ScrollArea className="w-full">
                <Table className="min-w-320 w-full">
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[200px]">Name</TableHead>
                      <TableHead className="w-[200px]">Address</TableHead>
                      <TableHead className="w-[200px]">Phone number</TableHead>
                      <TableHead className="w-[200px]">Owner name</TableHead>
                      <TableHead className="w-[200px] text-right">
                        Actions
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading
                      ? (
                          <TableRow>
                            <TableCell colSpan={5}>
                              <TableLoader length={15} />
                            </TableCell>
                          </TableRow>
                        )
                      : (
                          data?.getEstablishmentList?.data?.map(establishment => (
                            <TableRow key={establishment.id}>
                              <TableCell>{establishment.name}</TableCell>
                              <TableCell>{establishment.address}</TableCell>
                              <TableCell>{establishment.phone_number}</TableCell>
                              <TableCell>{establishment.owner.username}</TableCell>
                              <TableCell>
                                <div className="flex gap-x-2 justify-end">
                                  <CustomTooltip message="Update establishment">
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      onClick={() => {
                                        setSelectedEstablishment(establishment)
                                        toggleUpdateEstablishment(true)
                                      }}
                                    >
                                      <UpdateIcon />
                                    </Button>
                                  </CustomTooltip>
                                  <CustomTooltip message="Update owner">
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      onClick={() => {
                                        setSelectedOwner(establishment.owner)
                                        toggleUpdateOwner(true)
                                      }}
                                    >
                                      <UserUpdateIcon />
                                    </Button>
                                  </CustomTooltip>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                  </TableBody>
                </Table>
              </ScrollArea>
            )}
      </div>
      {totalPages > 1
        ? (
            <PagePagination
              currentPage={page}
              handlePagePagination={handlePageChange}
              lastPage={totalPages}
            />
          )
        : null}
      {selectedEstablishment
        ? (
            <UpdateEstablishmentDialog
              establishment={selectedEstablishment}
              open={openUpdateEstablishment}
              onOpenChange={(open) => {
                if (!open) {
                  setSelectedEstablishment(null)
                }
                toggleUpdateEstablishment(open)
              }}
            />
          )
        : null}
      {selectedOwner
        ? (
            <UpdateOwnerDialog
              owner={selectedOwner}
              open={openUpdateOwner}
              onOpenChange={(open) => {
                if (!open) {
                  setSelectedOwner(null)
                }
                toggleUpdateOwner(open)
              }}
            />
          )
        : null}
    </>
  )
}
