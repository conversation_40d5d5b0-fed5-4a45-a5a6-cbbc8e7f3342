/** @format */

import type { SelectedOwner } from './schema'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { But<PERSON> } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UpdateOwnerSchema } from './schema'
import useUpdateOwner from './use-update-owner'

interface Props {
  owner: SelectedOwner
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function UpdateOwnerDialog({
  owner,
  open,
  onOpenChange,
}: Props) {
  const queryClient = useQueryClient()
  const { updateOwner } = useUpdateOwner()

  const form = useForm({
    defaultValues: {
      username: owner.username,
      password: '',
      confirm_password: '',
    },
    validators: {
      onSubmit: UpdateOwnerSchema,
    },
    onSubmit: ({ value }) => {
      updateOwner.mutate(
        { id: owner.id, data: value },
        {
          onSuccess: () => {
            toast.success('Owner updated successfully')
            queryClient.invalidateQueries({ queryKey: ['establishment-list'] })
            form.reset()
            onOpenChange(false)
          },
          onError: (error) => {
            toast.error(parseGraphqlError(error))
          },
        },
      )
    },
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update owner</DialogTitle>
          <DialogDescription>Enter update details</DialogDescription>
        </DialogHeader>
        <form
          className="flex flex-col gap-4"
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
        >
          <form.Field name="username">
            {field => (
              <FormItem>
                <Label>Username</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter user name"
                />
                <FormMessage
                  errors={field.state.meta.errors
                    .map(e => e?.message)
                    .join(', ')}
                />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="password">
            {field => (
              <FormItem>
                <Label>Password</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter password"
                  type="password"
                />
                <FormMessage
                  errors={field.state.meta.errors
                    .map(e => e?.message)
                    .join(', ')}
                />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="confirm_password">
            {field => (
              <FormItem>
                <Label>Confirm password</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter confirm password"
                  type="password"
                />
                <FormMessage
                  errors={field.state.meta.errors
                    .map(e => e?.message)
                    .join(', ')}
                />
              </FormItem>
            )}
          </form.Field>
          <DialogFooter>
            <Button isLoading={updateOwner.isPending} type="submit">
              Update
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
