import type { CreateOwnerWithEstablishmentType } from './schema'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import useDialogStates from '~/hooks/use-dialog-states'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { CreateOwnerWithEstablishmentSchema } from './schema'
import useCreateOwnerWithEstablishment from './use-create-owner-with-establishment'

export default function CreateOwnerWithEstablishmentDialog() {
  const { open, toggleDialog } = useDialogStates()
  const { createOwnerWithEstablishment } = useCreateOwnerWithEstablishment()

  const queryClient = useQueryClient()

  const form = useForm({
    defaultValues: {
      username: '',
      password: '',
      confirm_password: '',
      establishment_name: '',
      establishment_address: '',
      wa_phone_number_id: '',
      establishment_phone_number: '',
      logo: undefined,
      subtitle: '',
    } as CreateOwnerWithEstablishmentType,
    validators: {
      onSubmit: CreateOwnerWithEstablishmentSchema,
    },
    onSubmit: ({ value }) => {
      createOwnerWithEstablishment.mutate({
        ...value,
      }, {
        onSuccess: () => {
          toast.success('Owner with establishment created successfully')
          form.reset()
          queryClient.invalidateQueries({ queryKey: ['establishment-list'] })
          toggleDialog(false)
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
        },
      })
    },
  })

  return (
    <Dialog open={open} onOpenChange={toggleDialog}>
      <DialogTrigger asChild>
        <Button>Add Establishment</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create new establishment</DialogTitle>
          <DialogDescription>Enter details</DialogDescription>
        </DialogHeader>
        <form
          className="flex flex-col gap-4"
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
        >
          <form.Field name="establishment_name">
            {field => (
              <FormItem>
                <Label>Name</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter establishment name"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="subtitle">
            {field => (
              <FormItem>
                <Label>Subtitle</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter subtitle"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="establishment_address">
            {field => (
              <FormItem>
                <Label>Address</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter establishment address"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="establishment_phone_number">
            {field => (
              <FormItem>
                <Label>Phone number</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter phone number"
                  maxLength={10}
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="logo">
            {field => (
              <FormItem>
                <Label>Logo</Label>
                <Input
                  onChange={(e) => {
                    if (e.target.files && e.target.files.length > 0) {
                      field.handleChange(e.target.files[0])
                    }
                    else {
                      field.handleChange(undefined)
                    }
                  }}
                  placeholder="Enter logo"
                  type="file"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <div className="border my-4" />
          <form.Field name="username">
            {field => (
              <FormItem>
                <Label>Username</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter user name"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="password">
            {field => (
              <FormItem>
                <Label>Password</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter password"
                  type="password"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <form.Field name="confirm_password">
            {field => (
              <FormItem>
                <Label>Confirm password</Label>
                <Input
                  value={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  placeholder="Enter confirm password"
                  type="password"
                />
                <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
              </FormItem>
            )}
          </form.Field>
          <Button
            isLoading={createOwnerWithEstablishment.isPending}
            type="submit"
          >
            Submit
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  )
}
