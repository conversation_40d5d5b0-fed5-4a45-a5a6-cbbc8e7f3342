import PageHeader from '~/components/common/page-header'
import DashboardQueries from './dashboard-queries'
import RecentReports from './recent-reports'
import Stats from './stats'
import DashboardTemplates from './templates'
import WeeklyLineCharts from './weekly-line-charts'

export default function OwnerDashboard() {
  return (
    <div className="flex flex-col grow">
      <PageHeader title="Dashboard" />
      <DashboardQueries />

      <div className="flex mt-8 h-100">
        <div className="basis-1/3 gap-8 flex flex-col justify-between">
          <Stats />
          <RecentReports />
        </div>
        <div className="basis-2/3 flex">
          <WeeklyLineCharts />
        </div>
      </div>

      <div className="my-4 border-2 border-dashed" />

      <DashboardTemplates />
    </div>
  )
}
