import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { parseAsString, useQueryState } from 'nuqs'
import { GET_STATS } from '~/graphql/queries/get-stats'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetStats() {
  const [startDate, setStartDate] = useQueryState('start_date', parseAsString)
  const [endDate, setEndDate] = useQueryState('end_date', parseAsString)

  const changeStartDate = (date: string) => {
    setStartDate(date)
  }

  const changeEndDate = (date: string) => {
    setEndDate(date)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-stats', startDate, endDate],
    queryFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: GET_STATS,
        variables: {
          start_date: startDate ? format(new Date(startDate), 'yyyy-MM-dd hh:mm:ss') : undefined,
          end_date: endDate ? format(new Date(endDate), 'yyyy-MM-dd hh:mm:ss') : undefined,
        },
      })
    },
  })

  return { data, isLoading, isError, startDate, endDate, changeStartDate, changeEndDate }
}
