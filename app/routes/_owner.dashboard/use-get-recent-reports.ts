import { useQuery } from '@tanstack/react-query'
import { GET_RECENT_REPORTS } from '~/graphql/queries/get-recent-reports'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetRecentReports() {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-recent-reports'],
    queryFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: GET_RECENT_REPORTS,
        variables: {
          first: 5,
        },
      })
    },
  })

  return { data, isLoading, isError }
}
