import {
  endOfMonth,
  endOfWeek,
  endOfYear,
  format,
  startOfMonth,
  startOfWeek,
  startOfYear,
} from 'date-fns'
import { useState } from 'react'
import { Button } from '~/components/ui/button'
import { Calendar } from '~/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '~/components/ui/popover'
import useGetStats from './use-get-stats'

type TimeRange = 'day' | 'week' | 'month' | 'year' | 'custom' | ''

export default function DashboardQueries() {
  const { changeStartDate, changeEndDate, startDate, endDate } = useGetStats()
  const [activeRange, setActiveRange] = useState<TimeRange>('')

  const formatDate = (date: Date) => format(date, 'yyyy-MM-dd')

  const handleDay = () => {
    const today = new Date()
    changeStartDate(formatDate(today))
    changeEndDate(formatDate(today))
    setActiveRange('day')
  }

  const handleWeek = () => {
    const today = new Date()
    const weekStart = startOfWeek(today, { weekStartsOn: 0 })
    const weekEnd = endOfWeek(today, { weekStartsOn: 0 })
    changeStartDate(formatDate(weekStart))
    changeEndDate(formatDate(weekEnd))
    setActiveRange('week')
  }

  const handleMonth = () => {
    const today = new Date()
    const monthStart = startOfMonth(today)
    const monthEnd = endOfMonth(today)
    changeStartDate(formatDate(monthStart))
    changeEndDate(formatDate(monthEnd))
    setActiveRange('month')
  }

  const handleYear = () => {
    const today = new Date()
    const yearStart = startOfYear(today)
    const yearEnd = endOfYear(today)
    changeStartDate(formatDate(yearStart))
    changeEndDate(formatDate(yearEnd))
    setActiveRange('year')
  }

  return (
    <div className="grid grid-cols-5 gap-x-8 w-full my-4">
      <Button
        variant={activeRange === 'day' ? 'default' : 'outline'}
        onClick={handleDay}
        className="col-span-1"
      >
        Day
      </Button>
      <Button
        variant={activeRange === 'week' ? 'default' : 'outline'}
        onClick={handleWeek}
        className="col-span-1"
      >
        Week
      </Button>
      <Button
        variant={activeRange === 'month' ? 'default' : 'outline'}
        onClick={handleMonth}
        className="col-span-1"
      >
        Month
      </Button>
      <Button
        variant={activeRange === 'year' ? 'default' : 'outline'}
        onClick={handleYear}
        className="col-span-1"
      >
        Year
      </Button>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={activeRange === 'custom' ? 'default' : 'outline'}
            className="col-span-1"
          >
            Date Range
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto flex">
          <div>
            <div className="text-center text-sm text-gray-500">Start date</div>
            <Calendar
              mode="single"
              defaultMonth={startDate ? new Date(startDate) : undefined}
              selected={startDate ? new Date(startDate) : undefined}
              onSelect={(date) => {
                if (date) {
                  changeStartDate(formatDate(date))
                }
                else {
                  changeStartDate('')
                }
                setActiveRange('custom')
              }}
            />
          </div>
          <div>
            <div className="text-center text-sm text-gray-500">End date</div>
            <Calendar
              mode="single"
              defaultMonth={endDate ? new Date(endDate) : undefined}
              selected={endDate ? new Date(endDate) : undefined}
              onSelect={(date) => {
                if (date) {
                  changeEndDate(formatDate(date))
                }
                else {
                  changeEndDate('')
                }
                setActiveRange('custom')
              }}
            />
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
