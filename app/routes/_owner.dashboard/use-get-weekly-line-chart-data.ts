import { useQuery } from '@tanstack/react-query'
import { GET_WEEKLY_LINE_CHART_DATA } from '~/graphql/queries/get-weekly-chart-data'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetWeeklyLineChartData() {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-weekly-line-chart-data'],
    queryFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: GET_WEEKLY_LINE_CHART_DATA,
      })
    },
  })

  return { data, isLoading, isError }
}
