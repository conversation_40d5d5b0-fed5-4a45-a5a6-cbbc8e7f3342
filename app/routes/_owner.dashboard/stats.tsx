import CommonError from '~/components/common/common-error'
import { Skeleton } from '~/components/ui/skeleton'
import useGetStats from './use-get-stats'

export default function Stats() {
  const { data, isLoading, isError } = useGetStats()

  if (isLoading) {
    return (
      <div className="flex gap-4 w-full">
        <Skeleton className="flex-1 h-26 bg-white" />
        <Skeleton className="flex-1 h-26 bg-white" />
      </div>
    )
  }

  if (isError) {
    return (
      <div className="border rounded-md col-span-2 h-32 bg-white">
        <CommonError message="Unable to load stats" />
      </div>
    )
  }

  return (
    <div className="flex gap-4 w-full">
      <div className="flex-1 flex flex-col bg-white items-center gap-y-4 border border-black rounded-md p-4">
        <div>Total Patients</div>
        <div className="text-xl font-bold">{data?.getStats?.total_patient ?? 0}</div>
      </div>

      <div className="flex-1 flex flex-col bg-white items-center gap-y-4 border border-black rounded-md p-4">
        <div>Total Number of Tests</div>
        <div className="text-xl font-bold">{data?.getStats?.total_tests_count ?? 0}</div>
      </div>
    </div>
  )
}
