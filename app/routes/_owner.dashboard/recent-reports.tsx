import { Link } from 'react-router'
import { Skeleton } from '~/components/ui/skeleton'
import useGetRecentReports from './use-get-recent-reports'

export default function RecentReports() {
  const { data, isLoading, isError } = useGetRecentReports()

  return (
    <Link to="/reports" className="flex flex-col gap-4 border rounded-md p-4 border-black bg-white grow">
      <div className="font-bold shrink-0">Recent activities</div>
      {isLoading && (
        <div className="flex flex-col gap-y-4">
          <Skeleton className="h-8" />
          <Skeleton className="h-8" />
          <Skeleton className="h-8" />
          <Skeleton className="h-8" />
          <Skeleton className="h-8" />
          <Skeleton className="h-8" />
        </div>
      )}
      {isError && (
        <div className="flex justify-center items-center h-full">
          Error. Unable to get recent reports.
        </div>
      )}
      {data?.getRecentReports?.length === 0 ? <div>No recent report available.</div> : null}
      {!isLoading
        && !isError
        && data?.getRecentReports?.map((item) => {
          return (
            <ul className="flex flex-col list-disc list-inside" key={item.id}>
              <li>
                {item?.template?.name}
                &nbsp; : &nbsp;
                {item?.patient?.name}
              </li>
            </ul>
          )
        })}
    </Link>
  )
}
