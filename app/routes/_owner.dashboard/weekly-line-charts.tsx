import type { ChartConfig } from '~/components/ui/chart'
import { CartesianGrid, Line, LineChart, XAxis } from 'recharts'
import LoaderIcon from '~/components/icons/loader-icon'
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '~/components/ui/chart'
import useGetWeeklyLineChartData from './use-get-weekly-line-chart-data'

export default function WeeklyLineCharts() {
  const { data, isLoading, isError } = useGetWeeklyLineChartData()

  const rawData = data?.getWeeklyLineChartData ?? []

  const templateNames = Array.from(new Set(rawData.map(item => item.template_name)))

  const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

  const dataByWeekday = rawData.reduce(
    (acc, { weekday, template_name, total_count }) => {
      if (!acc[weekday]) {
        acc[weekday] = {}
      }
      acc[weekday][template_name] = total_count
      return acc
    },
    {} as Record<string, Record<string, number>>,
  )

  const chartData = daysOfWeek.map((day) => {
    const Cdata: Record<string, string | number> = { weekday: day }
    templateNames.forEach((template) => {
      Cdata[template] = dataByWeekday[day]?.[template] || 0
    })
    return Cdata
  })

  chartData.forEach((dayData) => {
    let total = 0
    templateNames.forEach((template) => {
      total += (dayData[template] as number) || 0
    })
    dayData.all = total
  })

  if (rawData.length > 0) {
    templateNames.unshift('all')
  }

  const chartConfig = templateNames.reduce(
    (acc, templateName, index) => {
      acc[templateName] = {
        label: templateName,
        color: `var(--chart-${index + 1})`,
      }
      return acc
    },
    {} as ChartConfig,
  )

  return (
    <>
      <div className="p-8 border border-black rounded-md grow bg-white ml-8">
        {isLoading && (
          <div className="flex justify-center">
            <LoaderIcon className="animate-spin" />
          </div>
        )}
        {isError && (<div className="text-center">Error. Unable to load chart</div>)}
        {!isLoading && !isError && (
          <ChartContainer className="size-full" config={chartConfig}>
            <LineChart accessibilityLayer data={chartData}>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="weekday"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tickFormatter={value => value.slice(0, 3)}
                interval="preserveStartEnd"
              />
              <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
              {templateNames.map(templateName => (
                <Line
                  key={templateName}
                  dataKey={templateName}
                  type="linear"
                  stroke={chartConfig[templateName]?.color}
                  strokeWidth={2}
                  dot={true}
                />
              ))}
            </LineChart>
          </ChartContainer>
        )}
      </div>
    </>
  )
}
