import { Link } from 'react-router'
import CommonError from '~/components/common/common-error'
import PagePagination from '~/components/common/page-pagination'
import { Skeleton } from '~/components/ui/skeleton'
import useGetTemplates from '~/hooks/use-get-templates'

export default function DashboardTemplates() {
  const { data, isLoading, isError, totalPages, page, handlePageChange } = useGetTemplates()

  if (isLoading) {
    return (
      <div className="flex gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <Skeleton className="size-48 bg-white" key={index} />
        ))}
      </div>
    )
  }

  if (isError) {
    return <CommonError message="Error fetching templates" />
  }

  return (
    <>
      <div className="flex flex-col grow">
        <div className="flex flex-wrap gap-4">
          {data?.getTemplates.data?.map((item) => {
            return (
              <Link
                to={`/create-report/${item.slug}`}
                key={item.id}
                className="flex bg-white items-center justify-center border size-48 hover:bg-input border-black rounded-md p-4 text-center rounded-md"
              >
                {item.name}
              </Link>
            )
          })}
        </div>
      </div>
      {totalPages > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePageChange}
          lastPage={totalPages}
        />
      )}
    </>
  )
}
