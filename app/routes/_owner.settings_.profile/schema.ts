import { z } from 'zod'

export const UpdateMyProfileSchema = z.object({
  username: z.string().optional(),
  password: z.string().optional(),
  confirm_password: z.string().optional(),
}).refine(data => data.password === data.confirm_password, {
  message: 'Passwords do not match',
  path: ['confirm_password'],
})

export const UpdateMyEstablishmentSchema = z.object({
  name: z.string().optional(),
  phone_number: z.string().optional(),
  address: z.string().optional(),
  wa_phone_number_id: z.string().optional(),
  subtitle: z.string().optional(),
  logo: z.instanceof(File).optional(),
})

export type UpdateMyProfileType = z.infer<typeof UpdateMyProfileSchema>
export type UpdateMyEstablishmentType = z.infer<typeof UpdateMyEstablishmentSchema>
