import type { UpdateMyEstablishmentType } from './schema'
import type { GetMeQuery } from '~/gql/graphql'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UpdateMyEstablishmentSchema } from './schema'
import useUpdateMyEstablishment from './use-update-my-establishment'

interface Props {
  user: GetMeQuery
}

export default function UpdateEstablishmentForm({ user }: Props) {
  const { updateMyEstablishment } = useUpdateMyEstablishment()
  const queryClient = useQueryClient()

  const form = useForm({
    defaultValues: {
      name: user?.getMe?.establishment?.name || '',
      address: user?.getMe?.establishment?.address || '',
      phone_number: user?.getMe?.establishment?.phone_number || '',
      logo: undefined,
    } as UpdateMyEstablishmentType,
    validators: {
      onSubmit: UpdateMyEstablishmentSchema,
    },
    onSubmit: ({ value }) => {
      updateMyEstablishment.mutate(
        {
          ...value,
        },
        {
          onSuccess: () => {
            toast.success('Establishment updated successfully')
          },
          onError: (error) => {
            toast.error(parseGraphqlError(error))
          },
        },
      )
      queryClient.invalidateQueries({
        queryKey: ['get-me'],
      })
    },
  })

  return (
    <>
      <form
        className="flex flex-col gap-4"
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
      >
        <form.Field
          name="logo"
          children={({ state, handleChange }) => (
            <FormItem>
              <Label htmlFor="logo">Logo</Label>
              <Input
                id="logo"
                type="file"
                onChange={(e) => {
                  if (e.target.files && e.target.files.length > 0) {
                    handleChange(e.target.files[0])
                  }
                  else {
                    handleChange(undefined)
                  }
                }}
              />
              <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
            </FormItem>
          )}
        />
        <form.Field name="name">
          {field => (
            <FormItem>
              <Label>Establishment Name</Label>
              <Input
                value={field.state.value}
                onChange={e => field.handleChange(e.target.value)}
                placeholder="Enter establishment name"
              />
              <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
            </FormItem>
          )}
        </form.Field>
        <form.Field name="address">
          {field => (
            <FormItem>
              <Label>Establishment Address</Label>
              <Input
                value={field.state.value}
                onChange={e => field.handleChange(e.target.value)}
                placeholder="Enter establishment address"
              />
              <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
            </FormItem>
          )}
        </form.Field>
        <form.Field name="phone_number">
          {field => (
            <FormItem>
              <Label>Phone number</Label>
              <Input
                value={field.state.value}
                onChange={e => field.handleChange(e.target.value)}
                placeholder="Enter phone number"
                maxLength={10}
              />
              <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
            </FormItem>
          )}
        </form.Field>
        <div>
          <Button isLoading={updateMyEstablishment.isPending} type="submit">
            Update Establishment
          </Button>
        </div>
      </form>
    </>
  )
}
