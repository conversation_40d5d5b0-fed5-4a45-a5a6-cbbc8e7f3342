import type { UpdateMyProfileType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { UPDATE_MY_PROFILE } from '~/graphql/mutations/update-my-profile'
import { graphqlClient } from '~/lib/graphql-client'

export default function useUpdateMyProfile() {
  const updateMyProfile = useMutation({
    mutationFn: async (data: UpdateMyProfileType) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: UPDATE_MY_PROFILE,
        variables: {
          ...data,
        },
      })
    },
  })

  return { updateMyProfile }
}
