import type { UpdateMyEstablishmentType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { UPDATE_MY_ESTABLISHMENT } from '~/graphql/mutations/update-my-establishment'
import { graphqlClient } from '~/lib/graphql-client'

export default function useUpdateMyEstablishment() {
  const updateMyEstablishment = useMutation({
    mutationFn: async (data: UpdateMyEstablishmentType) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: UPDATE_MY_ESTABLISHMENT,
        variables: {
          ...data,
        },
      })
    },
  })

  return { updateMyEstablishment }
}
