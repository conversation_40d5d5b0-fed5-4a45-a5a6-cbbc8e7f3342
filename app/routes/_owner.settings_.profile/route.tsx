import BackButton from '~/components/common/back-button'
import CommonError from '~/components/common/common-error'
import PageHeader from '~/components/common/page-header'
import LoaderIcon from '~/components/icons/loader-icon'
import useGetMe from '~/hooks/use-get-me'
import baseUrl from '~/lib/base-url'
import UpdateEstablishmentForm from './update-establishment-form'
import UpdateProfileForm from './update-profile-form'

export default function SettingsProfile() {
  const { data, isLoading, isError } = useGetMe()

  if (isLoading) {
    return (
      <div className="flex justify-center items-center grow">
        <LoaderIcon className="size-16 animate-spin" />
      </div>
    )
  }

  if (isError) {
    return <CommonError />
  }

  const logoImgPath = data?.getMe?.establishment?.logo?.path || ''

  return (
    <div className="flex flex-col grow">
      <div className="mb-4">
        <BackButton />
      </div>
      <PageHeader title="Profile" />
      <div className="grid grid-cols-4 gap-8">
        {data && (
          <>
            <div className="col-span-1 border rounded-md p-4 flex flex-col gap-4">
              {logoImgPath
                ? (
                    <img src={`${baseUrl}/image/medium/${logoImgPath}`} alt="Logo" />
                  )
                : null}
              <UpdateEstablishmentForm user={data} />
            </div>
            <div className="col-span-1 ">
              <div className="border p-4 rounded-md">

                <UpdateProfileForm user={data} />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
