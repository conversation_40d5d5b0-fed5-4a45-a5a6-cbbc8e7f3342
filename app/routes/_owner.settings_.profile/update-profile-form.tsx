import type { UpdateMyProfileType } from './schema'
import type { GetMeQuery } from '~/gql/graphql'
import { useForm } from '@tanstack/react-form'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UpdateMyProfileSchema } from './schema'
import useUpdateMyProfile from './use-update-my-profile'

interface Props {
  user: GetMeQuery
}

export default function UpdateProfileForm({ user }: Props) {
  const { updateMyProfile } = useUpdateMyProfile()

  const form = useForm({
    defaultValues: {
      username: user?.getMe?.username || '',
      password: '',
      confirm_password: '',
    } as UpdateMyProfileType,
    validators: {
      onSubmit: UpdateMyProfileSchema,
    },
    onSubmit: ({ value }) => {
      updateMyProfile.mutate(
        {
          ...value,
        },
        {
          onSuccess: () => {
            toast.success('Profile updated successfully')
          },
          onError: (error) => {
            toast.error(parseGraphqlError(error))
          },
        },
      )
    },
  })

  return (
    <>
      <form
        className="flex flex-col gap-4"
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
      >
        <form.Field name="username">
          {field => (
            <FormItem>
              <Label>Username</Label>
              <Input
                value={field.state.value}
                onChange={e => field.handleChange(e.target.value)}
                placeholder="Enter username"
              />
              <FormMessage
                errors={field.state.meta.errors
                  .map(e => e?.message)
                  .join(', ')}
              />
            </FormItem>
          )}
        </form.Field>
        <form.Field name="password">
          {field => (
            <FormItem>
              <Label>Password</Label>
              <Input
                value={field.state.value}
                onChange={e => field.handleChange(e.target.value)}
                placeholder="Enter password"
              />
              <FormMessage
                errors={field.state.meta.errors
                  .map(e => e?.message)
                  .join(', ')}
              />
            </FormItem>
          )}
        </form.Field>
        <form.Field name="confirm_password">
          {field => (
            <FormItem>
              <Label>Confirm password</Label>
              <Input
                value={field.state.value}
                onChange={e => field.handleChange(e.target.value)}
                placeholder="Confirm password"
              />
              <FormMessage
                errors={field.state.meta.errors
                  .map(e => e?.message)
                  .join(', ')}
              />
            </FormItem>
          )}
        </form.Field>
        <div>
          <Button
            isLoading={updateMyProfile.isPending}
            type="submit"
          >
            Update Profile
          </Button>
        </div>
      </form>
    </>
  )
}
