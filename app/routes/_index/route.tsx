import type { Route } from './+types/route'
import { useForm } from '@tanstack/react-form'
import { useMutation } from '@tanstack/react-query'
import { data, redirect, useFetcher } from 'react-router'
import { toast } from 'sonner'
import { z } from 'zod'
import { <PERSON><PERSON> } from '~/components/ui/button'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { LOGIN } from '~/graphql/mutations/login'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { commitSession, destroySession, getSession } from '~/sessions'

export async function action({ request }: Route.ActionArgs) {
  const session = await getSession(request.headers.get('Cookie'))
  const formData = await request.formData()

  const role = formData.get('role')
  const token = formData.get('token') // don't really need token for this project but useful for dev mode for testing auth
  if (!role || !token) {
    return data(
      { error: 'Invalid session data' },
      {
        headers: {
          'Set-Cookie': await destroySession(session),
        },
      },
    )
  }

  session.set('role', role?.toString())
  session.set('token', token?.toString())

  if (role === 'superadmin') {
    return redirect('/establishments', {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    })
  }
  else if (role === 'owner') {
    return redirect('/dashboard', {
      headers: {
        'Set-Cookie': await commitSession(session),
      },
    })
  }
}

const schema = z.object({
  username: z.string().min(3, { message: 'Username must be at least 3 characters' }).max(20, { message: 'Username must be less than 20 characters' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
})

type FormType = z.infer<typeof schema>

export default function Index() {
  const fetcher = useFetcher()

  const login = useMutation({
    mutationFn: async (data: FormType) => {
      const client = await graphqlClient()
      return await client.request({
        document: LOGIN,
        variables: {
          ...data,
        },
      })
    },
  })

  const form = useForm({
    defaultValues: {
      username: '',
      password: '',
    },
    validators: {
      onSubmit: schema,
    },
    onSubmit: ({ value }) => {
      login.mutate(value, {
        onSuccess: (data) => {
          const formData = new FormData()
          const role = data.login.user.role
          const token = data.login.token

          formData.append('role', role)
          formData.append('token', token)

          fetcher.submit(formData, {
            method: 'POST',
          })
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
          console.error(error)
        },
      })
    },
  })

  return (
    <div className="flex h-screen w-full flex-col items-center justify-center">
      <h1 className="text-xl font-bold">MEDLAB</h1>
      <div>Please login to continue</div>
      <form
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
        className="flex flex-col gap-y-6 mt-4 max-w-72 w-full"
      >
        <form.Field
          name="username"
          children={({ state, handleBlur, handleChange }) => (
            <FormItem>
              <Label className="sr-only">Username</Label>
              <Input
                value={state.value}
                onBlur={handleBlur}
                onChange={e => handleChange(e.target.value)}
                placeholder="Username"
              />
              <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
            </FormItem>
          )}
        />
        <form.Field
          name="password"
          children={({ state, handleBlur, handleChange }) => (
            <FormItem>
              <Label className="sr-only">Password</Label>
              <Input
                value={state.value}
                onBlur={handleBlur}
                onChange={e => handleChange(e.target.value)}
                type="password"
                placeholder="Password"
              />
              <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
            </FormItem>
          )}
        />
        <form.Subscribe
          selector={state => [state.canSubmit, state.isSubmitting]}
          children={([canSubmit, isSubmitting]) => (
            <Button type="submit" isLoading={login.isPending} disabled={!canSubmit}>
              {isSubmitting ? '...' : 'Login'}
            </Button>
          )}
        />
      </form>

    </div>
  )
}
