import { Link } from 'react-router'
import BackButton from '~/components/common/back-button'
import CommonError from '~/components/common/common-error'
import { CardLoader } from '~/components/common/common-loaders'
import PageHeader from '~/components/common/page-header'
import PagePagination from '~/components/common/page-pagination'
import PlusIcon from '~/components/icons/plus-icon'
import useGetTemplates from '~/hooks/use-get-templates'

export default function Settings() {
  const { data, isLoading, isError, totalPages, page, handlePageChange } = useGetTemplates()

  if (isLoading) {
    return (
      <div className="flex">
        <CardLoader length={15} className="flex flex-wrap gap-4" />
      </div>
    )
  }

  if (isError) {
    return <CommonError message="Error fetching templates" />
  }

  return (
    <div className="flex flex-col grow">
      <div className="mb-4">
        <BackButton />
      </div>
      <PageHeader title="Templates" />
      <div className="flex flex-col grow">
        <div className="flex flex-wrap gap-4 mt-4">
          {data?.getTemplates.data?.map((item) => {
            return (
              <Link to={`/settings/templates/${item.slug}`} key={item.id} className="flex items-center justify-center border size-48 hover:bg-input bg-white p-4 text-center rounded-md">
                {item.name}
              </Link>
            )
          })}
          <Link to="/settings/create-template" className="col-span-1 flex items-center justify-center border size-48 bg-white hover:bg-input p-4 text-center rounded-md">
            <PlusIcon className="size-12" />
          </Link>
        </div>

      </div>
      {totalPages > 1 && (
        <PagePagination
          currentPage={page}
          handlePagePagination={handlePageChange}
          lastPage={totalPages}
        />
      )}
    </div>
  )
}
