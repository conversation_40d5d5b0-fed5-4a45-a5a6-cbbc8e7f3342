import type { FormEvent } from 'react'
import type { UpdateReportType } from './schema'
import type { SelectedPatient } from '~/lib/types/selected-patient'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { format } from 'date-fns'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router'
import { toast } from 'sonner'
import AddPatientDialog from '~/components/common/add-patient-dialog'
import BackButton from '~/components/common/back-button'
import CommonError from '~/components/common/common-error'
import PageHeader from '~/components/common/page-header'
import Tiptap from '~/components/common/tip-tap'
import LoaderIcon from '~/components/icons/loader-icon'
import { Button } from '~/components/ui/button'
import { Checkbox } from '~/components/ui/checkbox'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group'
import { Textarea } from '~/components/ui/textarea'
import parseFieldInputType from '~/lib/parse-field-input-type'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { cn } from '~/lib/utils'
import AddPatientFromList from '~/routes/_owner.create-report_.$slug/add-patient-from-list'
import SelectedPatientCard from '~/routes/_owner.create-report_.$slug/selected-patient-card'
import useGetReportById from '~/routes/_owner.reports_.$id_.update/use-get-report-by-id'
import useUpdateReport from '~/routes/_owner.reports_.$id_.update/use-update-report'
import RefDoctorCombobox from '../../components/common/ref-doctor-combobox'
import { UpdateReportSchema } from './schema'

export default function UpdateReport() {
  const { id } = useParams()

  const queryClient = useQueryClient()

  const { updateReport } = useUpdateReport()
  const { data, isLoading, isError } = useGetReportById({ id: Number(id) })

  const [selectedPatient, setSelectedPatient]
    = useState<SelectedPatient | null>(null)

  const handleSelectPatient = (patient: SelectedPatient | null) => {
    setSelectedPatient(patient)
  }

  const form = useForm({
    defaultValues: {
      template_id: 0,
      sections_input: [],
      fields_input: [],
      remarks: '',
      staff: '',
      test_date: '',
      collection_date: '',
      report_generation_date: '',
      doctor_id: undefined,
    } as UpdateReportType,
    validators: { onSubmit: UpdateReportSchema },
    onSubmit: ({ value }) => {
      if (selectedPatient) {
        updateReport.mutate(
          { data: value, id: Number(id), patient_id: selectedPatient.id },
          {
            onSuccess: () => {
              toast.success('Report updated successfully')
              queryClient.invalidateQueries({
                queryKey: ['get-report-by-id', Number(id)],
              })
              queryClient.invalidateQueries({ queryKey: ['get-test-reports'] })
            },
            onError: (error) => {
              toast.error(parseGraphqlError(error))
            },
          },
        )
      }
      else {
        toast.error('Please select patient')
      }
    },
  })

  useEffect(() => {
    if (data?.getReportById?.template && data?.getReportById?.template_data) {
      const patient = data.getReportById.patient
      const template = data.getReportById.template
      const sectionsInput
        = data.getReportById.template_data.sections?.map(section => ({
          name: section.name,
          fields:
            section.fields?.map(field => ({
              input_type: parseFieldInputType(field.input_type ?? ''),
              name: field.name ?? '',
              name_value: field.name_value ?? '',
              reference: field.reference ?? '',
              reference_active: field.reference_active ?? false,
              reference_value: field.reference_value ?? '',
              unit: field.unit ?? '',
              unit_value: field.unit_value ?? '',
              unit_active: field.unit_active ?? false,
              input_type_values: field.input_type_values ?? [],
              sub_fields:
                field.sub_fields?.map(subField => ({
                  input_type: parseFieldInputType(subField.input_type ?? ''),
                  name: subField.name ?? '',
                  name_value: subField.name_value ?? '',
                  unit: subField.unit ?? '',
                  unit_value: subField.unit_value ?? '',
                  unit_active: subField.unit_active ?? false,
                  reference: subField.reference ?? '',
                  reference_active: subField.reference_active ?? false,
                  reference_value: subField.reference_value ?? '',
                })) ?? [],
            })) ?? [],
        })) ?? []
      form.setFieldValue('template_id', template.id)
      form.setFieldValue('sections_input', sectionsInput)
      form.setFieldValue('fields_input', [])
      form.setFieldValue('remarks', data.getReportById.remarks ?? '')
      form.setFieldValue('staff', data.getReportById.staff ?? '')
      form.setFieldValue(
        'test_date',
        data.getReportById.test_date
          ? format(new Date(data.getReportById.test_date), 'yyyy-MM-dd')
          : '',
      )
      form.setFieldValue(
        'collection_date',
        data.getReportById.collection_date
          ? format(new Date(data.getReportById.collection_date), 'yyyy-MM-dd')
          : '',
      )
      form.setFieldValue(
        'report_generation_date',
        data.getReportById.report_generation_date
          ? format(
              new Date(data.getReportById.report_generation_date),
              'yyyy-MM-dd',
            )
          : '',
      )
      form.setFieldValue(
        'doctor_id',
        data.getReportById.doctor?.id ?? undefined,
      )
      handleSelectPatient(patient)
    }
  }, [data, form])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center grow">
        <LoaderIcon className="size-16 animate-spin" />
      </div>
    )
  }

  if (isError || !data?.getReportById?.template) {
    return <CommonError />
  }

  const template = data.getReportById.template
  const sections = template.template_data.sections ?? []

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    e.stopPropagation()
    form.handleSubmit()
  }

  return (
    <div className="flex flex-col gap-4 ">
      <div className="flex justify-between">
        <div>
          <BackButton />
        </div>
      </div>
      <div className="flex justify-center space-x-8 my-4">
        <AddPatientFromList onSelect={handleSelectPatient} />
        <AddPatientDialog onSuccessCallback={handleSelectPatient} />
      </div>
      {selectedPatient
        ? (
            <div className="flex justify-center">
              <SelectedPatientCard
                patient={selectedPatient}
                removePatient={() => handleSelectPatient(null)}
              />
            </div>
          )
        : null}
      <form onSubmit={handleSubmit} className="flex flex-col gap-4">
        <div className="flex justify-center">
          <PageHeader title={template.name} />
        </div>
        <div className="flex gap-x-4">
          <FormItem className="basis-1/4">
            <Label htmlFor="ref_doctor">Ref. Doctor</Label>
            <RefDoctorCombobox
              onSelect={(value) => {
                const doctorId = Number.parseInt(value)
                form.setFieldValue('doctor_id', doctorId)
              }}
              selectedId={
                data?.getReportById?.doctor?.id?.toString() ?? undefined
              }
            />
          </FormItem>
          <form.Field
            name="collection_date"
            children={({ state, handleChange }) => (
              <FormItem className="basis-1/4">
                <Label htmlFor="collection_date">Collection Date</Label>
                <Input
                  id="collection_date"
                  type="date"
                  value={state.value}
                  onChange={e => handleChange(e.target.value)}
                  className="block"
                />
              </FormItem>
            )}
          />
          <form.Field
            name="test_date"
            children={({ state, handleChange }) => (
              <FormItem className="basis-1/4">
                <Label htmlFor="test_date">Test Date</Label>
                <Input
                  id="test_date"
                  type="date"
                  value={state.value}
                  onChange={e => handleChange(e.target.value)}
                  className="block"
                />
              </FormItem>
            )}
          />
          <form.Field
            name="report_generation_date"
            children={({ state, handleChange }) => (
              <FormItem className="basis-1/4">
                <Label htmlFor="report_generation_date">
                  Report Generation Date
                </Label>
                <Input
                  id="report_generation_date"
                  type="date"
                  value={state.value}
                  onChange={e => handleChange(e.target.value)}
                  className="block"
                />
              </FormItem>
            )}
          />
        </div>
        <form.Field name="sections_input" mode="array">
          {sectionsField => (
            <>
              {sectionsField.state.value
                && sectionsField.state.value.length > 0 && (
                <div>
                  {sectionsField.state.value?.map((_, i) => (
                    <div
                      key={i}
                      className="flex flex-col gap-4 py-8 border-b border-input"
                    >
                      {/* <div className="text-2xl font-bold">{sections[i].name}</div> */}
                      <div className="grid grid-cols-12 gap-4 max-w-4xl mx-auto w-full">
                        <div className="font-bold col-span-3">
                          {sections[i].name}
                        </div>
                        <div className="font-bold col-span-4">Result</div>
                        {sections[i].fields?.some(
                          field => field.unit_active,
                        )
                          ? (
                              <div className="font-bold col-span-2">Unit</div>
                            )
                          : (
                              <div className="col-span-2" />
                            )}
                        {sections[i].fields?.some(
                          field => field.reference_active,
                        )
                          ? (
                              <div className="font-bold col-span-3">
                                BIO.REF.INTERVAL
                              </div>
                            )
                          : (
                              <div className="col-span-3" />
                            )}
                      </div>

                      <form.Field
                        name={`sections_input[${i}].fields`}
                        mode="array"
                      >
                        {(fieldsSubField) => {
                          return (
                            <div className="flex flex-col">
                              {fieldsSubField.state.value?.map((_, j) => {
                                const fields = sections?.[i]?.fields?.[j]
                                return (
                                  <div
                                    key={j}
                                    className={cn('flex flex-col p-2 py-2', {
                                      'bg-gray-100': j % 2 === 0,
                                    })}
                                  >
                                    <div className="max-w-4xl mx-auto grid grid-cols-12 gap-4 w-full">
                                      <Label
                                        className={cn('items-start pt-3', {
                                          'col-span-2':
                                              fields?.input_type === 'subfield',
                                          'col-span-3':
                                              fields?.input_type !== 'subfield',
                                        })}
                                      >
                                        {fields?.name}
                                      </Label>
                                      {fields?.input_type && (
                                        <>
                                          {fields.input_type
                                            === 'textfield' && (
                                            <form.Field
                                              name={`sections_input[${i}].fields[${j}].name_value`}
                                            >
                                              {subField => (
                                                <Input
                                                  tabIndex={i}
                                                  className="col-span-4"
                                                  value={subField.state.value}
                                                  onChange={e =>
                                                    subField.handleChange(
                                                      e.target.value,
                                                    )}
                                                />
                                              )}
                                            </form.Field>
                                          )}
                                          {fields.input_type
                                            === 'richtext' && (
                                            <form.Field
                                              name={`sections_input[${i}].fields[${j}].name_value`}
                                            >
                                              {subField => (
                                                <div
                                                  className={cn(
                                                    'col-span-4 bg-white',
                                                    {
                                                      'col-span-9':
                                                          !fields.unit_active
                                                          && !fields.reference_active,
                                                    },
                                                  )}
                                                >
                                                  <Tiptap
                                                    tabIndex={i}
                                                    id={`sections_input[${i}].fields[${j}].name_value`}
                                                    name={`sections_input[${i}].fields[${j}].name_value`}
                                                    value={
                                                      subField.state.value
                                                    }
                                                    onChange={value =>
                                                      subField.handleChange(
                                                        value,
                                                      )}
                                                  />
                                                </div>
                                              )}
                                            </form.Field>
                                          )}
                                          {fields.input_type === 'subfield'
                                            && fields.sub_fields
                                            && fields.sub_fields.length > 0 && (
                                            <div className="col-span-10 flex flex-col gap-4">
                                              <form.Field
                                                name={`sections_input[${i}].fields[${j}].sub_fields`}
                                                mode="array"
                                              >
                                                {subField => (
                                                  <>
                                                    {subField.state.value?.map(
                                                      (_, k) => {
                                                        const subField
                                                              = fields
                                                                ?.sub_fields?.[
                                                                  k
                                                                ]
                                                        return (
                                                          <div key={k}>
                                                            <form.Field
                                                              name={`sections_input[${i}].fields[${j}].sub_fields[${k}].name_value`}
                                                            >
                                                              {subFieldField => (
                                                                <div className="grid grid-cols-10 gap-x-4 w-full ">
                                                                  <Label className="col-span-1">
                                                                    {
                                                                      subField?.name
                                                                    }
                                                                  </Label>
                                                                  <Input
                                                                    tabIndex={i}
                                                                    className="col-span-4"
                                                                    value={
                                                                      subFieldField
                                                                        .state
                                                                        .value
                                                                    }
                                                                    onChange={e =>
                                                                      subFieldField.handleChange(
                                                                        e
                                                                          .target
                                                                          .value,
                                                                      )}
                                                                  />
                                                                  {subField?.unit_active
                                                                    ? (
                                                                        <div className="col-span-2">
                                                                          <form.Field
                                                                            name={`sections_input[${i}].fields[${j}].sub_fields[${k}].unit_value`}
                                                                          >
                                                                            {subFieldUnit => (
                                                                              <Input
                                                                                tabIndex={-1}
                                                                                value={
                                                                                  subFieldUnit
                                                                                    .state
                                                                                    .value
                                                                                }
                                                                                onChange={e =>
                                                                                  subFieldUnit.handleChange(
                                                                                    e
                                                                                      .target
                                                                                      .value,
                                                                                  )}
                                                                              />
                                                                            )}
                                                                          </form.Field>
                                                                        </div>
                                                                      )
                                                                    : (
                                                                        <div className="col-span-2" />
                                                                      )}
                                                                  {subField?.reference_active
                                                                    ? (
                                                                        <div className="col-span-3">
                                                                          <form.Field
                                                                            name={`sections_input[${i}].fields[${j}].sub_fields[${k}].reference_value`}
                                                                          >
                                                                            {subFieldUnit => (
                                                                              <Textarea
                                                                                tabIndex={-1}
                                                                                value={
                                                                                  subFieldUnit
                                                                                    .state
                                                                                    .value
                                                                                }
                                                                                onChange={e =>
                                                                                  subFieldUnit.handleChange(
                                                                                    e
                                                                                      .target
                                                                                      .value,
                                                                                  )}
                                                                              />
                                                                            )}
                                                                          </form.Field>
                                                                        </div>
                                                                      )
                                                                    : (
                                                                        <div className="col-span-3" />
                                                                      )}
                                                                </div>
                                                              )}
                                                            </form.Field>
                                                          </div>
                                                        )
                                                      },
                                                    )}
                                                  </>
                                                )}
                                              </form.Field>
                                            </div>
                                          )}
                                          {fields.input_type === 'select'
                                            && fields.input_type_values
                                            && fields.input_type_values.length
                                            > 0 && (
                                            <>
                                              <form.Field
                                                name={`sections_input[${i}].fields[${j}].name_value`}
                                              >
                                                {subField => (
                                                  <>
                                                    <div className="col-span-8">
                                                      <RadioGroup
                                                        value={
                                                          subField.state
                                                            .value
                                                        }
                                                        onValueChange={(
                                                          e,
                                                        ) => {
                                                          subField.handleChange(
                                                            e,
                                                          )
                                                        }}
                                                        className="flex gap-x-6"
                                                      >
                                                        {fields.input_type_values?.map(
                                                          value => (
                                                            <Label
                                                              key={value}
                                                            >
                                                              <RadioGroupItem
                                                                tabIndex={i}
                                                                className="bg-white"
                                                                value={
                                                                  value
                                                                }
                                                              />
                                                              {value}
                                                            </Label>
                                                          ),
                                                        )}
                                                      </RadioGroup>
                                                    </div>
                                                  </>
                                                )}
                                              </form.Field>
                                            </>
                                          )}
                                          {fields.input_type
                                            === 'multiselect'
                                            && fields.input_type_values
                                            && fields.input_type_values.length
                                            > 0 && (
                                            <>
                                              <form.Field
                                                name={`sections_input[${i}].fields[${j}].input_type_values`}
                                                mode="array"
                                              >
                                                {subField => (
                                                  <>
                                                    <div className="col-span-8 flex flex-col gap-2">
                                                      {fields.input_type_values?.map(
                                                        value => (
                                                          <Label
                                                            key={value}
                                                          >
                                                            <Checkbox
                                                              tabIndex={i}
                                                              className="bg-white"
                                                              checked={subField.state.value?.includes(
                                                                value,
                                                              )}
                                                              onCheckedChange={(
                                                                isChecked,
                                                              ) => {
                                                                const currentFormValues
                                                                      = subField
                                                                        .state
                                                                        .value
                                                                        || []
                                                                let newFormValues
                                                                if (
                                                                  isChecked
                                                                  === true
                                                                ) {
                                                                  // Add value if it's not already there
                                                                  newFormValues
                                                                        = [
                                                                      ...new Set(
                                                                        [
                                                                          ...currentFormValues,
                                                                          value,
                                                                        ],
                                                                      ),
                                                                    ]
                                                                }
                                                                else {
                                                                  // Remove value
                                                                  newFormValues
                                                                        = currentFormValues.filter(
                                                                      v =>
                                                                        v
                                                                        !== value,
                                                                    )
                                                                }
                                                                subField.handleChange(
                                                                  newFormValues,
                                                                )
                                                              }}
                                                            />
                                                            {value}
                                                          </Label>
                                                        ),
                                                      )}
                                                    </div>
                                                  </>
                                                )}
                                              </form.Field>
                                            </>
                                          )}
                                        </>
                                      )}
                                      {fields?.unit_active
                                        ? (
                                            <form.Field
                                              name={`sections_input[${i}].fields[${j}].unit_value`}
                                            >
                                              {subField => (
                                                <Input
                                                  tabIndex={-1}
                                                  className="bg-white col-span-2"
                                                  value={subField.state.value}
                                                  onChange={e =>
                                                    subField.handleChange(
                                                      e.target.value,
                                                    )}
                                                  placeholder="Subfield text"
                                                />
                                              )}
                                            </form.Field>
                                          )
                                        : (
                                            <div className="col-span-2" />
                                          )}
                                      {fields?.reference_active
                                        ? (
                                            <form.Field
                                              name={`sections_input[${i}].fields[${j}].reference_value`}
                                            >
                                              {subField => (
                                                <Textarea
                                                  tabIndex={-1}
                                                  className="col-span-3 bg-white"
                                                  value={subField.state.value}
                                                  onChange={e =>
                                                    subField.handleChange(
                                                      e.target.value,
                                                    )}
                                                />
                                              )}
                                            </form.Field>
                                          )
                                        : (
                                            <div className="col-span-3" />
                                          )}
                                    </div>
                                  </div>
                                )
                              })}
                            </div>
                          )
                        }}
                      </form.Field>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </form.Field>
        <div className="max-w-4xl mx-auto w-full">
          <form.Field
            name="remarks"
            children={({ state, handleBlur, handleChange }) => (
              <div className="w-full grid grid-cols-12 gap-4">
                <div className="col-span-3" />

                <FormItem className="col-span-5">
                  <Label className="sr-only">Remarks</Label>
                  <Textarea
                    className="w-full"
                    value={state.value}
                    onBlur={handleBlur}
                    onChange={e => handleChange(e.target.value)}
                    placeholder="Remarks"
                  />
                  <FormMessage
                    errors={state.meta.errors.map(e => e?.message).join(', ')}
                  />
                </FormItem>
                <div className="col-span-4" />
              </div>
            )}
          />
          <form.Field
            name="staff"
            children={({ state, handleBlur, handleChange }) => (
              <div className="w-full grid grid-cols-12 gap-4">
                <div className="col-span-3" />

                <FormItem className="col-span-4">
                  <Label className="sr-only">Staff</Label>
                  <Input
                    className="w-full"
                    value={state.value}
                    onBlur={handleBlur}
                    onChange={e => handleChange(e.target.value)}
                    placeholder="Staff info"
                  />
                  <FormMessage
                    errors={state.meta.errors.map(e => e?.message).join(', ')}
                  />
                </FormItem>
                <div className="col-span-4" />
              </div>
            )}
          />
        </div>
        <div className="flex justify-center">
          <Button type="submit" isLoading={updateReport.isPending}>
            Submit Report
          </Button>
        </div>
      </form>
    </div>
  )
}
