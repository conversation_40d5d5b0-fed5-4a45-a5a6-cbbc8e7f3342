import type { UpdateReportType } from '~/routes/_owner.reports_.$id_.update/schema'
import { useMutation } from '@tanstack/react-query'
import { format } from 'date-fns'
import { UPDATE_REPORT } from '~/graphql/mutations/update-report'
import { graphqlClient } from '~/lib/graphql-client'

export default function useUpdateReport() {
  const updateReport = useMutation({
    mutationFn: async ({
      data,
      id,
      patient_id,
    }: {
      data: UpdateReportType
      id: number
      patient_id: number
    }) => {
      const client = await graphqlClient()
      return await client.request({
        document: UPDATE_REPORT,
        variables: {
          ...data,
          id,
          patient_id,
          collection_date: data.collection_date
            ? format(data.collection_date, 'yyyy-MM-dd HH:mm:ss')
            : undefined,
          test_date: data.test_date
            ? format(data.test_date, 'yyyy-MM-dd HH:mm:ss')
            : undefined,
          report_generation_date: data.report_generation_date
            ? format(data.report_generation_date, 'yyyy-MM-dd HH:mm:ss')
            : undefined,
        },
      })
    },
  })

  return { updateReport }
}
