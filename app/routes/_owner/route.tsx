import type { Route } from './+types/route'
import { Outlet, redirect } from 'react-router'
import { SidebarProvider, SidebarTrigger } from '~/components/ui/sidebar'
import { destroySession, getSession } from '~/sessions'
import OwnerSidebar from './owner-sidebar'

export const meta = [
  {
    title: 'Medlab',
  },
]

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get('Cookie'))

  const role = session.get('role')

  if (role !== 'owner') {
    return redirect('/', {
      headers: {
        'Set-Cookie': await destroySession(session),
      },
    })
  }

  return { role }
}

function Owner() {
  return (
    <div className="mx-auto flex size-full min-h-screen flex-col w-full overflow-hidden">
      <SidebarProvider>
        <div className="flex grow overflow-auto">
          <OwnerSidebar />
          <main className="flex grow flex-col p-2 bg-gray-100">
            <SidebarTrigger />
            <div className="flex grow flex-col mt-4 pb-4">
              <Outlet />
            </div>
          </main>
        </div>
      </SidebarProvider>
    </div>
  )
}

export default Owner
