import { Link } from 'react-router'
import PageHeader from '~/components/common/page-header'

const menu = [
  { name: 'Profile', path: 'profile' },
  { name: 'Templates', path: 'templates' },
  { name: '<PERSON>f Doctor List', path: 'doctors' },
  { name: 'PDF Settings', path: 'pdf-settings' },
]

export default function OwnerSettings() {
  return (
    <div className="flex flex-col grow">
      <PageHeader title="Settings" />
      <div className="flex flex-col grow">
        <div className="flex flex-wrap gap-4 mt-4">
          {menu?.map((item) => {
            return (
              <Link to={`/settings/${item.path}`} key={item.name} className="flex items-center justify-center border size-48 bg-white hover:bg-input p-4 text-center rounded-md">
                {item.name}
              </Link>
            )
          })}
        </div>
      </div>
    </div>
  )
}
