import type { Route } from './+types/route'
import { Outlet, redirect } from 'react-router'
import { SidebarProvider, SidebarTrigger } from '~/components/ui/sidebar'
import { destroySession, getSession } from '~/sessions'
import SuperadminSidebar from './superadmin-sidebar'

export const meta = [
  {
    title: 'Medlab - Superadmin',
  },
]

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get('<PERSON>ie'))

  const role = session.get('role')

  if (role !== 'superadmin') {
    return redirect('/', {
      headers: {
        'Set-Cookie': await destroySession(session),
      },
    })
  }

  return { role }
}

function Owner() {
  return (
    <div className="mx-auto flex size-full min-h-screen flex-col w-full overflow-hidden">
      <SidebarProvider>
        <div className="flex grow overflow-auto">
          <SuperadminSidebar />
          <main className="flex grow flex-col p-2">
            <SidebarTrigger />
            <div className="flex grow flex-col mt-4">
              <Outlet />
            </div>
          </main>
        </div>
      </SidebarProvider>
    </div>
  )
}

export default Owner
