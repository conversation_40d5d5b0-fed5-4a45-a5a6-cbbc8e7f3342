import { Link, useFetcher, useLocation } from 'react-router'
import LogoutIcon from '~/components/icons/logout-icon'
import { Button } from '~/components/ui/button'
import { Sidebar, SidebarContent, SidebarGroup, SidebarGroupContent, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '~/components/ui/sidebar'
import useLogout from '~/hooks/use-logout'
import { cn } from '~/lib/utils'

const menu = [
  {
    title: 'Establishments',
    url: '/establishments',
  },
]

export default function SuperadminSidebar() {
  const location = useLocation()
  const fetcher = useFetcher()
  const { logout } = useLogout()

  const handleLogout = () => {
    logout.mutate(undefined, {
      onError: () => {
        void fetcher.submit(null, {
          action: '/logout',
          method: 'POST',
        })
      },
      onSuccess: () => {
        void fetcher.submit(null, {
          action: '/logout',
          method: 'POST',
        })
      },
    })
  }

  return (
    <Sidebar>
      <SidebarContent className="flex flex-col">
        <SidebarGroup className="grow">
          <SidebarGroupContent>
            <SidebarMenu>
              {menu.map(item => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link
                      className={cn('w-full hover:bg-gray-100', {
                        'bg-gray-200 text-black':
                          location.pathname === item.url,
                      })}
                      to={item.url}
                    >
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarMenu>
            <SidebarMenuItem>
              <Button
                onClick={handleLogout}
                isLoading={logout.isPending}
                variant="destructive"
                className="w-full"
              >
                <LogoutIcon className="mr-1" />
                Log Out
              </Button>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
