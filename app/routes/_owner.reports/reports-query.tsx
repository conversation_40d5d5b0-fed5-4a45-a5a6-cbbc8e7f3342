import { X } from 'lucide-react'
import DatePicker from '~/components/common/datepicker'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import useGetTestReports from '~/routes/_owner.reports/use-get-test-reports'

export default function ReportsQuery() {
  const {
    startDate,
    endDate,
    handleEndDateChange,
    handleStartDateChange,
    reportName,
    patientInfo,
    handleReportNameChange,
    handlePatientInfoChange,
  } = useGetTestReports()

  return (
    <div className="flex gap-x-4 my-4">
      <Input
        className="basis-full md:basis-1/5"
        value={reportName}
        onChange={e => handleReportNameChange(e.target.value)}
        placeholder="Search by report name"
      />
      <Input
        className="basis-full md:basis-1/5"
        value={patientInfo}
        onChange={e => handlePatientInfoChange(e.target.value)}
        placeholder="Search by patient name/phone number"
      />
      <div className="basis-full md:basis-1/5 flex">
        <div className="w-full">
          <DatePicker value={startDate} handleDate={handleStartDateChange} placeholder="Start date" />
        </div>
        {startDate
          ? (

              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  handleStartDateChange(undefined)
                }}
              >
                <X />
              </Button>
            )
          : null}
      </div>
      <div className="basis-full md:basis-1/5 flex">
        <div className="w-full">
          <DatePicker value={endDate} handleDate={handleEndDateChange} placeholder="End date" />
        </div>
        {endDate
          ? (

              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  handleEndDateChange(undefined)
                }}
              >
                <X />
              </Button>
            )
          : null}
      </div>
    </div>
  )
}
