import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { GET_TEST_REPORTS } from '~/graphql/queries/get-test-reports'
import { graphqlClient } from '~/lib/graphql-client'
import { parseDateTime } from '~/lib/parse-date-time'

const first = 15

export default function useGetTestReports() {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))
  const [startDate, setStartDate] = useQueryState('start_date', parseAsString.withDefault(''))
  const [endDate, setEndDate] = useQueryState('end_date', parseAsString.withDefault(''))
  const [reportName, setReportName] = useQueryState('report_name', parseAsString.withDefault(''))
  const [patientInfo, setPatientInfo] = useQueryState('patient_info', parseAsString.withDefault(''))

  const [reportKeyword] = useDebounce(reportName, 500)
  const [patientKeyword] = useDebounce(patientInfo, 500)

  const handleStartDateChange = (date: string | undefined) => {
    // Use null instead of undefined for useQueryState
    setStartDate(date ?? null)
    setPage(1)
  }

  const handleEndDateChange = (date: string | undefined) => {
    // Use null instead of undefined for useQueryState
    setEndDate(date ?? null)
    setPage(1)
  }

  const handleReportNameChange = (name: string) => {
    setReportName(name)
    setPage(1)
  }

  const handlePatientInfoChange = (info: string) => {
    setPatientInfo(info)
    setPage(1)
  }

  const handlePageChange = (page: number) => {
    setPage(page)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-test-reports', page, startDate, endDate, reportKeyword, patientKeyword],
    queryFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: GET_TEST_REPORTS,
        variables: {
          first,
          page,
          start_date: startDate ? parseDateTime(startDate) : undefined,
          end_date: endDate ? parseDateTime(endDate) : undefined,
          report_name: reportKeyword,
          patient_info: patientKeyword,
        },
      })
    },
  })

  const totalItems = data?.getTestReports?.paginator_info?.total || 0
  const totalPages = Math.ceil(totalItems / first) || 1

  return { data, isLoading, isError, handlePageChange, page, totalPages, handleStartDateChange, handleEndDateChange, startDate, endDate, handleReportNameChange, handlePatientInfoChange, reportName, patientInfo }
}
