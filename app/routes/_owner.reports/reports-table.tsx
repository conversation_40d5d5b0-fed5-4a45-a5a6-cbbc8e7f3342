import type { TestReport } from '~/gql/graphql'
import type { DeepPartial } from '~/lib/types/deep-partial'
import { pdf } from '@react-pdf/renderer'
import { useQueryClient } from '@tanstack/react-query'
import { format } from 'date-fns'
import { Info } from 'lucide-react'
import { useState } from 'react'
import { Link } from 'react-router'
import { toast } from 'sonner'
import CommonError from '~/components/common/common-error'
import { TableLoader } from '~/components/common/common-loaders'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import CustomTooltip from '~/components/common/custom-tooltip'
import PagePagination from '~/components/common/page-pagination'
import ReportPDFDocument from '~/components/common/report-pdf'
import SendTestReportDialog from '~/components/common/send-test-report-dialog'
import DeleteIcon from '~/components/icons/delete-icon'
import DownloadIcon from '~/components/icons/download-icon'
import LoaderIcon from '~/components/icons/loader-icon'
import PrintIcon from '~/components/icons/print-icon'
import UpdateIcon from '~/components/icons/update-icon'
import ViewIcon from '~/components/icons/view-icon'
import WhatsappIcon from '~/components/icons/whatsapp-icon'
import { Button, buttonVariants } from '~/components/ui/button'
import { Checkbox } from '~/components/ui/checkbox'
import { Label } from '~/components/ui/label'
import { ScrollArea } from '~/components/ui/scroll-area'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import useDialogStates from '~/hooks/use-dialog-states'
import useGetPdfSettings from '~/hooks/use-get-pdf-settings'
import baseUrl from '~/lib/base-url'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { cn } from '~/lib/utils'
import useDeleteReport from '~/routes/_owner.reports/use-delete-report'
import useGetTestReports from '~/routes/_owner.reports/use-get-test-reports'

export default function ReportsTable() {
  const [selectedReportId, setSelectedReportId] = useState<number | null>(null)
  const [hideEmptyFields, setHideEmptyFields] = useState(false)
  const [downloadingReportId, setDownloadingReportId] = useState<number | null>(
    null,
  ) // Added state for download loading
  const [selectedReport, setSelectedReport]
    = useState<DeepPartial<TestReport> | null>(null)

  const queryClient = useQueryClient()
  const { open: openDeleteDialog, toggleDialog: toggleDeleteDialog }
    = useDialogStates()
  const { open: openSendReportDialog, toggleDialog: toggleSendReportDialog }
    = useDialogStates()

  const { data, isLoading, isError, handlePageChange, page, totalPages }
    = useGetTestReports()
  const {
    data: pdfSettings,
    isLoading: isLoadingPdfSettings,
    isError: isErrorPdfSettings,
  } = useGetPdfSettings()

  const { deleteReport } = useDeleteReport()

  const handleDeleteReport = () => {
    if (!selectedReportId)
      return
    deleteReport.mutate(selectedReportId, {
      onSuccess: () => {
        toast.success('Report deleted successfully')
        toggleDeleteDialog(false)
        queryClient.invalidateQueries({ queryKey: ['get-test-reports'] })
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  const handlePrintReport = async (report: DeepPartial<TestReport>) => {
    const imagePath = pdfSettings?.getPdfSetting?.logo?.path || ''
    const signaturePath = pdfSettings?.getPdfSetting?.establishment?.signature?.path || ''

    let imgData: string | undefined
    let signatureData: string | undefined

    if (signaturePath) {
      const response = await fetch(`${baseUrl}/image/medium/${signaturePath}`)
      const imageBlob = await response.blob()
      signatureData = await new Promise<string>((resolve) => {
        const reader = new FileReader()
        reader.onloadend = () => {
          resolve(reader.result as string)
        }
        reader.readAsDataURL(imageBlob)
      })
    }

    if (imagePath) {
      const response = await fetch(`${baseUrl}/image/medium/${imagePath}`)
      const imageBlob = await response.blob()
      imgData = await new Promise<string>((resolve) => {
        const reader = new FileReader()
        reader.onloadend = () => {
          resolve(reader.result as string)
        }
        reader.readAsDataURL(imageBlob)
      })
    }
    const blob = await pdf(
      <ReportPDFDocument
        reportData={report}
        imgData={imgData}
        pdfSettings={pdfSettings?.getPdfSetting}
        signatureData={signatureData}
        hideEmptyFields={hideEmptyFields}
      />,
    ).toBlob()
    const myWindow = window.open(URL.createObjectURL(blob), '_blank')
    if (myWindow) {
      myWindow.open()
      myWindow.focus()
      myWindow.print()
      // Use afterprint event to close the window after printing or cancelling
      myWindow.addEventListener('afterprint', () => {
        myWindow.close()
        URL.revokeObjectURL(myWindow.location.href) // Revoke the object URL
      })
    }
  }

  const handleSendTestReport = async (report: DeepPartial<TestReport>) => {
    setSelectedReport(report)
    toggleSendReportDialog(true)
  }

  const handleDownloadReport = async (report: DeepPartial<TestReport>) => {
    if (report)
      setDownloadingReportId(report.id!)
    try {
      const imagePath = pdfSettings?.getPdfSetting?.logo?.path || ''
      const signaturePath = pdfSettings?.getPdfSetting?.establishment?.signature?.path || ''
      let signatureData: string | undefined
      let imgData: string | undefined

      if (signaturePath) {
        const response = await fetch(`${baseUrl}/image/medium/${signaturePath}`)
        const imageBlob = await response.blob()
        signatureData = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onloadend = () => {
            resolve(reader.result as string)
          }
          reader.readAsDataURL(imageBlob)
        })
      }

      if (imagePath) {
        const response = await fetch(`${baseUrl}/image/medium/${imagePath}`)
        const imageBlob = await response.blob()
        imgData = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onloadend = () => {
            resolve(reader.result as string)
          }
          reader.readAsDataURL(imageBlob)
        })
      }

      const blob = await pdf(
        <ReportPDFDocument
          reportData={report}
          imgData={imgData}
          pdfSettings={pdfSettings?.getPdfSetting}
          signatureData={signatureData}
          hideEmptyFields={hideEmptyFields}
        />,
      ).toBlob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `report-${report.id}.pdf`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
    catch (error) {
      console.error('Error generating or downloading PDF:', error)
      toast.error('Failed to download report.')
    }
    finally {
      setDownloadingReportId(null)
    }
  }

  if (isLoading || isLoadingPdfSettings) {
    return (
      <div className="py-4">
        <TableLoader length={15} />
      </div>
    )
  }

  if (isError || isErrorPdfSettings) {
    return <CommonError message="Error fetching reports" />
  }

  return (
    <>
      <div className="flex flex-col grow">
        <div className="flex justify-end my-4">
          <Label className="bg-black text-white px-4 py-2 rounded-md">
            <Checkbox
              checked={hideEmptyFields}
              className="bg-white data-[state=checked]:bg-green-500 data-[state=checked]:text-black"
              onCheckedChange={(e) => {
                if (e) {
                  setHideEmptyFields(true)
                }
                else {
                  setHideEmptyFields(false)
                }
              }}
            />
            Hide empty fields in PDF
          </Label>
        </div>
        <ScrollArea>
          <Table className="min-w-320 table-fixed w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[10%]">Test</TableHead>
                <TableHead className="w-[10%]">Name</TableHead>
                <TableHead className="w-[10%]">Address</TableHead>
                <TableHead className="w-[10%]">Remarks</TableHead>
                <TableHead className="w-[10%]">Staff</TableHead>
                <TableHead className="w-[15%]">
                  Report generation date
                </TableHead>
                <TableHead className="w-[8%]">Status</TableHead>
                <TableHead className="w-[17%] text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading
                ? (
                    <TableLoader length={15} />
                  )
                : (
                    data?.getTestReports?.data?.map(report => (
                      <TableRow key={report.id}>
                        <TableCell className="whitespace-normal">
                          {report.template.name ?? '-'}
                        </TableCell>
                        <TableCell className="whitespace-normal">
                          {report.patient.name ?? '-'}
                        </TableCell>
                        <TableCell className="whitespace-normal">
                          {report.patient.address ?? '-'}
                        </TableCell>
                        <TableCell className="whitespace-normal">
                          {report.remarks ?? '-'}
                        </TableCell>
                        <TableCell className="whitespace-normal">
                          {report.staff ?? '-'}
                        </TableCell>
                        <TableCell>
                          {report.created_at
                            ? format(
                                new Date(report.created_at),
                                'yyyy-MM-dd hh:mm:ss a',
                              )
                            : '-'}
                        </TableCell>
                        <TableCell className="whitespace-normal">
                          {report.latestWhatsAppReceipt?.message_status ?? '-'}
                          {' '}
                          {report.latestWhatsAppReceipt?.error_message
                            ? (
                                <CustomTooltip
                                  message={report.latestWhatsAppReceipt?.error_message}
                                >
                                  <Info />
                                </CustomTooltip>
                              )
                            : null}
                        </TableCell>
                        <TableCell>
                          <div className="flex justify-end gap-x-2">
                            <CustomTooltip message="View report">
                              <Link
                                to={`/reports/${report.id}`}
                                className={cn(
                                  buttonVariants({
                                    variant: 'outline',
                                    size: 'icon',
                                  }),
                                )}
                              >
                                <ViewIcon />
                              </Link>
                            </CustomTooltip>
                            <CustomTooltip message="Download report">
                              <Button
                                onClick={() => handleDownloadReport(report)}
                                variant="outline"
                                size="icon"
                                disabled={downloadingReportId === report.id}
                              >
                                {downloadingReportId === report.id
                                  ? (
                                      <LoaderIcon className="animate-spin" />
                                    )
                                  : (
                                      <DownloadIcon />
                                    )}
                              </Button>
                            </CustomTooltip>
                            <CustomTooltip message="Send to whatsapp">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleSendTestReport(report)}
                              >
                                <WhatsappIcon />
                              </Button>
                            </CustomTooltip>
                            <CustomTooltip message="Print report">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handlePrintReport(report)}
                              >
                                <PrintIcon />
                              </Button>
                            </CustomTooltip>
                            <CustomTooltip message="Update report">
                              <Link
                                to={`/reports/${report.id}/update`}
                                className={cn(
                                  buttonVariants({
                                    variant: 'outline',
                                    size: 'icon',
                                  }),
                                )}
                              >
                                <UpdateIcon />
                              </Link>
                            </CustomTooltip>
                            <CustomTooltip message="Delete report">
                              <Button
                                onClick={() => {
                                  setSelectedReportId(report.id)
                                  toggleDeleteDialog(true)
                                }}
                                variant="outline"
                                size="icon"
                              >
                                <DeleteIcon />
                              </Button>
                            </CustomTooltip>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
            </TableBody>
          </Table>
        </ScrollArea>
      </div>
      {totalPages > 1
        ? (
            <PagePagination
              currentPage={page}
              handlePagePagination={handlePageChange}
              lastPage={totalPages}
            />
          )
        : null}
      {selectedReportId && (
        <ConfirmationDialog
          handleConfirm={handleDeleteReport}
          handleOpenChange={toggleDeleteDialog}
          isPending={deleteReport.isPending}
          open={openDeleteDialog}
        />
      )}
      {selectedReport && (
        <SendTestReportDialog
          open={openSendReportDialog}
          onOpenChange={() => {
            toggleSendReportDialog(false)
            setSelectedReport(null)
          }}
          report={selectedReport}
          pdfSettings={pdfSettings?.getPdfSetting}
          hideEmptyFields={hideEmptyFields}
        />
      )}
    </>
  )
}
