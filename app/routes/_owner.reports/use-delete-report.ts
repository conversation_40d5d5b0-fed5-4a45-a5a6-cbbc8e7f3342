import { useMutation } from '@tanstack/react-query'
import { DELETE_REPORT } from '~/graphql/mutations/delete-report'
import { graphqlClient } from '~/lib/graphql-client'

export default function useDeleteReport() {
  const deleteReport = useMutation({
    mutationFn: async (id: number) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: DELETE_REPORT,
        variables: {
          id,
        },
      })
    },
  })

  return { deleteReport }
}
