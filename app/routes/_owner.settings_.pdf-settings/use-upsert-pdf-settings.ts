import type { UpsertPdfSettingsType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { UPSERT_PDF_SETTINGS } from '~/graphql/mutations/upsert-pdf-settings'
import { graphqlClient } from '~/lib/graphql-client'

export default function useUpsertPdfSettings() {
  const upsertPdfSettings = useMutation({
    mutationFn: async ({ data, establishment_id }: { data: UpsertPdfSettingsType, establishment_id?: number }) => {
      const graphql = await graphqlClient()

      return await graphql.request({
        document: UPSERT_PDF_SETTINGS,
        variables: {
          ...data,
          establishment_id,
        },
      })
    },
  })

  return { upsertPdfSettings }
}
