import { z } from 'zod'

export const UpsertPdfSettingsSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  subtitle: z.string().min(1, 'Subtitle is required'),
  footer_left: z.string().optional(),
  footer_right: z.string().optional(),
  footer_center: z.string().optional(),
  logo: z.instanceof(File).optional(),
  left_signature: z.instanceof(File).optional(),
  right_signature: z.instanceof(File).optional(),
})

export type UpsertPdfSettingsType = z.infer<typeof UpsertPdfSettingsSchema>
