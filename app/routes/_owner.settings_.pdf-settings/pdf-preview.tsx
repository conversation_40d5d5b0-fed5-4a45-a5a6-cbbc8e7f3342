import type { GetPdfSettingQuery } from '~/gql/graphql'
import { Document, Image, Page, PDFViewer, StyleSheet, Text, View } from '@react-pdf/renderer'
import { useEffect, useState } from 'react'
import baseUrl from '~/lib/base-url'

interface Props {
  pdfSettings: GetPdfSettingQuery['getPdfSetting'] | null | undefined
}

const styles = StyleSheet.create({
  document: {
    backgroundColor: '#FFFFFF',
    width: '100%',
    display: 'flex',
  },
  page: {
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
    padding: 30,
    fontSize: 10, // Default text size
    position: 'relative', // Added for absolute positioning of footer
  },
  patientSection: { display: 'flex', flexDirection: 'row', marginBottom: 15 },
  patientInfoColumn: {
    display: 'flex',
    flex: 1,
    flexDirection: 'column',
    gap: 4,
  },
  patientDataRow: { flexDirection: 'row', display: 'flex' },
  patientLabelCell: {
    flexBasis: '35%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingRight: 5,
    display: 'flex',
  },
  patientValueCell: { flexBasis: '65%', flexWrap: 'wrap' },
  dateLabelCell: {
    flexBasis: '65%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingRight: 5,
  },
  dateValueCell: { flexBasis: '35%', flexWrap: 'wrap' },
  boldText: { fontWeight: 'bold' },

  reportTitle: {
    fontSize: 20,
    textAlign: 'center',
    marginVertical: 15,
    fontWeight: 'bold',
  },

  sectionCard: {
    display: 'flex',
    flex: 1,
    flexDirection: 'column',
    marginBottom: 32,
    padding: 8,
  }, // Changed border: 1 to borderWidth: 1

  imageBlock: {
    width: 50,
    height: 50,
    position: 'absolute',
    top: 0,
    left: 0,
  },
  headerSection: {
    display: 'flex',
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    marginBottom: 15,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#000',
  },

  titleBlock: {
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
    alignItems: 'center',
    width: '100%',
    flex: 1,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    width: '100%',
    textAlign: 'center',
  },
  subtitle: { width: '100%', textAlign: 'center', whiteSpace: 'pre-wrap' },
  reference: { whiteSpace: 'pre-wrap' },
  preWrap: { whiteSpace: 'pre-wrap' },
  footerContainer: {
    display: 'flex',
    flexDirection: 'row',
    whiteSpace: 'pre-wrap',
  },
  bottomFooterLeft: {
    textAlign: 'left',
    whiteSpace: 'pre-wrap',
    basis: '33.33%',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  bottomFooterCenter: {
    whiteSpace: 'pre-wrap',
    height: '100%',
    basis: '33.33%',
    width: '100%',
    textAlign: 'center',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  bottomFooterRight: {
    textAlign: 'right',
    whiteSpace: 'pre-wrap',
    basis: '33.33%',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
})

export default function PDFPreview({ pdfSettings }: Props) {
  const [logoDataUrl, setLogoDataUrl] = useState<string>()
  const []
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!pdfSettings?.logo?.path) {
      return
    }
    setIsLoading(true)
    fetch(`${baseUrl}/image/medium/${pdfSettings.logo.path}`)
      .then(res => res.blob())
      .then(
        blob =>
          new Promise<string>((resolve) => {
            const reader = new FileReader()
            reader.onloadend = () => resolve(reader.result as string)
            reader.readAsDataURL(blob)
          }),
      )
      .then(setLogoDataUrl)
      .finally(() => setIsLoading(false))
  }, [pdfSettings])

  return (
    !isLoading && (
      <PDFViewer style={styles.document}>
        <Document style={styles.document}>
          <Page size="A4" style={styles.page}>
            <View style={styles.headerSection}>
              {logoDataUrl
                ? (
                    <View style={styles.imageBlock}>
                      <Image src={logoDataUrl} />
                    </View>
                  )
                : null}
              <View style={styles.titleBlock}>
                <Text style={styles.title}>{pdfSettings?.title}</Text>
                <Text style={styles.subtitle}>{pdfSettings?.subtitle}</Text>
              </View>
            </View>

            <View style={styles.patientSection}>
              <View style={styles.patientInfoColumn}>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Name</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Age</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Gender</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Phone</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Address</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Blood Group</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.patientLabelCell}>
                    <Text style={styles.boldText}>Ref. Doctor</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.patientValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
              </View>

              <View style={styles.patientInfoColumn}>
                <View style={styles.patientDataRow}>
                  <View style={styles.dateLabelCell}>
                    <Text style={styles.boldText}>Collection Date</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.dateValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.dateLabelCell}>
                    <Text style={styles.boldText}>Test Date</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.dateValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
                <View style={styles.patientDataRow}>
                  <View style={styles.dateLabelCell}>
                    <Text style={styles.boldText}>Report Generation Date</Text>
                    <Text>:</Text>
                  </View>
                  <View style={styles.dateValueCell}>
                    <Text>-</Text>
                  </View>
                </View>
              </View>
            </View>
            <Text style={styles.reportTitle}>Report name</Text>
            <View style={styles.sectionCard}></View>
            <View style={styles.footerContainer}>
              <View style={styles.bottomFooterLeft}>
                <Text>{pdfSettings?.footer_left}</Text>
              </View>
              <View style={styles.bottomFooterCenter}>
                <Text>{pdfSettings?.footer_center}</Text>
              </View>
              <View style={styles.bottomFooterRight}>
                <Text>{pdfSettings?.footer_right}</Text>
              </View>
            </View>
          </Page>
        </Document>
      </PDFViewer>
    )
  )
}
