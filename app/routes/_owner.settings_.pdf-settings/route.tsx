import BackButton from '~/components/common/back-button'
import CommonError from '~/components/common/common-error'
import LoaderIcon from '~/components/icons/loader-icon'
import useGetPdfSettings from '~/hooks/use-get-pdf-settings'
import PDFPreview from './pdf-preview'
import PdfSettingsForm from './pdf-settings-form'

export default function PDFSettings() {
  const { data, isLoading, isError } = useGetPdfSettings()

  if (isLoading) {
    return (
      <div className="flex justify-center items-center grow">
        <LoaderIcon className="size-16 animate-spin" />
      </div>
    )
  }

  if (isError) {
    return <CommonError />
  }

  const pdfSettings = data?.getPdfSetting

  return (
    <div className="flex flex-col grow">
      <div className="mb-4">
        <BackButton />
      </div>
      <div className="grid grid-cols-2 gap-4 grow">
        <div className="col-span-1">
          {!isLoading ? <PdfSettingsForm pdfSettings={pdfSettings} /> : null}
        </div>
        <div className="col-span-1 grow flex">
          <PDFPreview
            pdfSettings={pdfSettings}
          />
        </div>
      </div>
    </div>
  )
}
