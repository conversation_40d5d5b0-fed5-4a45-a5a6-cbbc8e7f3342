import type { UpsertPdfSettingsType } from './schema'
import type { GetPdfSettingQuery } from '~/gql/graphql'
import { useForm } from '@tanstack/react-form'
import { useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { Button } from '~/components/ui/button'
import FormItem from '~/components/ui/form-item'
import FormMessage from '~/components/ui/form-message'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Textarea } from '~/components/ui/textarea'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UpsertPdfSettingsSchema } from './schema'
import useUpsertPdfSettings from './use-upsert-pdf-settings'

interface Props {
  pdfSettings: GetPdfSettingQuery['getPdfSetting'] | null | undefined
}

export default function PdfSettingsForm({ pdfSettings }: Props) {
  const { upsertPdfSettings } = useUpsertPdfSettings()
  const queryClient = useQueryClient()

  const form = useForm({
    defaultValues: {
      footer_center: pdfSettings?.footer_center ?? '',
      footer_left: pdfSettings?.footer_left ?? '',
      footer_right: pdfSettings?.footer_right ?? '',
      logo: undefined,
      subtitle: pdfSettings?.subtitle ?? '',
      title: pdfSettings?.title ?? '',
      left_signature: undefined,
      right_signature: undefined,
    } as UpsertPdfSettingsType,
    validators: {
      onSubmit: UpsertPdfSettingsSchema,
    },
    onSubmit: ({ value }) => {
      upsertPdfSettings.mutate(
        { data: value },
        {
          onSuccess: () => {
            toast.success('Pdf settings updated successfully')
            queryClient.invalidateQueries({
              queryKey: ['get-pdf-settings'],
            })
            form.reset()
          },
          onError: (error) => {
            toast.error(parseGraphqlError(error))
          },
        },
      )
    },
  })

  return (
    <form
      className="max-w-1/2 flex flex-col gap-4"
      onSubmit={(e) => {
        e.preventDefault()
        e.stopPropagation()
        form.handleSubmit()
      }}
    >
      <form.Field
        name="logo"
        children={({ state, handleChange }) => (
          <FormItem>
            <Label htmlFor="logo">Logo</Label>
            <Input
              id="logo"
              type="file"
              onChange={(e) => {
                if (e.target.files && e.target.files.length > 0) {
                  handleChange(e.target.files[0])
                }
                else {
                  handleChange(undefined)
                }
              }}
            />
            <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
          </FormItem>
        )}
      />
      <form.Field
        name="title"
        children={({ state, handleChange }) => (
          <FormItem className="w-full">
            <Label htmlFor="title">Title</Label>
            <Input id="title" value={state.value} onChange={e => handleChange(e.target.value)} />
            <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
          </FormItem>
        )}
      />
      <form.Field
        name="subtitle"
        children={({ state, handleChange }) => (
          <FormItem>
            <Label htmlFor="subtitle">Subtitle</Label>
            <Textarea
              id="subtitle"
              value={state.value}
              onChange={e => handleChange(e.target.value)}
            />
            <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
          </FormItem>
        )}
      />
      <form.Field
        name="footer_left"
        children={({ state, handleChange }) => (
          <FormItem>
            <Label htmlFor="footer_left">Footer Left</Label>
            <Textarea
              id="footer_left"
              value={state.value}
              onChange={e => handleChange(e.target.value)}
            />
            <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
          </FormItem>
        )}
      />
      <form.Field
        name="left_signature"
        children={({ state, handleChange }) => (
          <FormItem>
            <Label htmlFor="left_signature">Left signature</Label>
            <Input
              id="left_signature"
              type="file"
              onChange={(e) => {
                if (e.target.files && e.target.files.length > 0) {
                  handleChange(e.target.files[0])
                }
                else {
                  handleChange(undefined)
                }
              }}
            />
            <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
          </FormItem>
        )}
      />
      <form.Field
        name="footer_center"
        children={({ state, handleChange }) => (
          <FormItem>
            <Label htmlFor="footer_center">Footer Center</Label>
            <Textarea
              id="footer_center"
              value={state.value}
              onChange={e => handleChange(e.target.value)}
            />
            <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
          </FormItem>
        )}
      />
      <form.Field
        name="footer_right"
        children={({ state, handleChange }) => (
          <FormItem>
            <Label htmlFor="footer_right">Footer Right</Label>
            <Textarea
              id="footer_right"
              value={state.value}
              onChange={e => handleChange(e.target.value)}
            />
            <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
          </FormItem>
        )}
      />
      <form.Field
        name="right_signature"
        children={({ state, handleChange }) => (
          <FormItem>
            <Label htmlFor="right_signature">Right signature</Label>
            <Input
              id="right_signature"
              type="file"
              onChange={(e) => {
                if (e.target.files && e.target.files.length > 0) {
                  handleChange(e.target.files[0])
                }
                else {
                  handleChange(undefined)
                }
              }}
            />
            <FormMessage errors={state.meta.errors.map(e => e?.message).join(', ')} />
          </FormItem>
        )}
      />
      <Button type="submit" isLoading={upsertPdfSettings.isPending}>
        Submit
      </Button>
    </form>
  )
}
