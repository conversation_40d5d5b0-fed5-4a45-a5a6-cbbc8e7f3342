import type { Route } from './+types/root'

import { MutationCache, QueryCache, QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ClientError } from 'graphql-request'
import { NuqsAdapter } from 'nuqs/adapters/react-router/v7'
import { useState } from 'react'
import {
  isRouteErrorResponse,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from 'react-router'
import { Toaster } from '~/components/ui/sonner'
import baseUrl from './lib/base-url'
import './app.css'

export const links: Route.LinksFunction = () => [
  { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
  {
    rel: 'preconnect',
    href: 'https://fonts.gstatic.com',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'stylesheet',
    href: 'https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap',
  },
]

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export default function App() {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000,
            retry: (failureCount, error) => {
              if (error instanceof ClientError) {
                if (error?.response?.status === 419) {
                  return false
                }
                return failureCount < 2
              }

              return false
            },
          },
        },
        mutationCache: new MutationCache({
          onError: async (error) => {
            if (error instanceof ClientError) {
              if (error.response?.status === 419) {
                console.error('Handling 419 CSRF token error')

                try {
                  await fetch(`${baseUrl}/csrf`, {
                    credentials: 'include',
                  })

                  // Important: Instead of navigating away, retry the failed queries
                  const failedQueries = queryClient.getQueryCache().findAll({
                    predicate: query => query.state.status === 'error',
                  })

                  // Wait a moment for the cookie to be set
                  setTimeout(() => {
                    failedQueries.forEach((query) => {
                      queryClient.refetchQueries({ queryKey: query.queryKey })
                    })
                  }, 100)
                }
                catch (refreshError) {
                  console.error('Failed to refresh CSRF token:', refreshError)
                  // Only navigate away if token refresh fails
                  globalThis.location.href = '/'
                }
              }
              else {
                error?.response?.errors?.map((error) => {
                  // alert("unauthenticated error")
                  if (error.message === 'Unauthenticated.') {
                    globalThis.location.href = '/logout'
                  }
                  return null
                })
              }
            }
          },
        }),
        queryCache: new QueryCache({
          onError: async (error) => {
            if (error instanceof ClientError) {
              if (error.response?.status === 419) {
                console.error('Handling 419 CSRF token error')

                try {
                  await fetch(`${baseUrl}/csrf`, {
                    credentials: 'include',
                  })

                  // Important: Instead of navigating away, retry the failed queries
                  const failedQueries = queryClient.getQueryCache().findAll({
                    predicate: query => query.state.status === 'error',
                  })

                  // Wait a moment for the cookie to be set
                  setTimeout(() => {
                    failedQueries.forEach((query) => {
                      queryClient.refetchQueries({ queryKey: query.queryKey })
                    })
                  }, 100)
                }
                catch (refreshError) {
                  console.error('Failed to refresh CSRF token:', refreshError)
                  // Only navigate away if token refresh fails
                  globalThis.location.href = '/'
                }
              }
              else {
                error?.response?.errors?.map((error) => {
                  // alert("unauthenticated error")
                  if (error.message === 'Unauthenticated.') {
                    globalThis.location.href = '/logout'
                  }

                  return null
                })
              }
            }
          },
        }),
      }),
  )

  return (
    <QueryClientProvider client={queryClient}>
      <Toaster />
      <NuqsAdapter>
        <Outlet />
      </NuqsAdapter>
    </QueryClientProvider>
  )
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  let message = 'Oops!'
  let details = 'An unexpected error occurred.'
  let stack: string | undefined

  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? '404' : 'Error'
    details
      = error.status === 404
        ? 'The requested page could not be found.'
        : error.statusText || details
  }
  else if (import.meta.env.DEV && error && error instanceof Error) {
    details = error.message
    stack = error.stack
  }

  return (
    <main className="pt-16 p-4 container mx-auto">
      <h1>{message}</h1>
      <p>{details}</p>
      {stack && (
        <pre className="w-full p-4 overflow-x-auto">
          <code>{stack}</code>
        </pre>
      )}
    </main>
  )
}
