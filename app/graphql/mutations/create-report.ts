import { graphql } from '~/gql'

export const CREATE_REPORT = graphql(`
  mutation CreateReport(
    $template_id: Int!
    $sections_input: [SectionInput!]
    $fields_input: [FieldInput!]
    $pdf: Upload
    $patient_id: Int!
    $remarks: String
    $staff: String
    $collection_date: DateTime
    $test_date: DateTime
    $report_generation_date: DateTime
    $doctor_id: Int
  ) {
    createReport(
      template_id: $template_id
      sections_input: $sections_input
      fields_input: $fields_input
      pdf: $pdf
      patient_id: $patient_id
      remarks: $remarks
      staff: $staff
      collection_date: $collection_date
      test_date: $test_date
      report_generation_date: $report_generation_date
      doctor_id: $doctor_id
    ) {
      message
    }
  }
`)
