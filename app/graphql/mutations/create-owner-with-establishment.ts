import { graphql } from '~/gql'

export const CREATE_OWNER_WITH_ESTABLISHMENT = graphql(`
  mutation CreateOwnerWithEstablishment(
    $username: String!
    $password: String!
    $establishment_name: String!
    $establishment_phone_number: String!
    $establishment_address: String
    $wa_phone_number_id: String
    $logo: Upload
  ) {
    createOwnerWithEstablishment(
      username: $username
      password: $password
      establishment_name: $establishment_name
      establishment_phone_number: $establishment_phone_number
      establishment_address: $establishment_address
      wa_phone_number_id: $wa_phone_number_id
      logo: $logo
    ) {
      message
    }
  }
`)
