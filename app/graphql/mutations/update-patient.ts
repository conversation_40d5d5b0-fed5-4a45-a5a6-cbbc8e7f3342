import { graphql } from '~/gql'

export const UPDATE_PATIENT = graphql(`
  mutation UpdatePatient(
    $id: Int!
    $name: String
    $age: Int
    $gender: String
    $blood_group: String
    $address: String,
    $phone_number: String
  ) {
    updatePatient(
      id: $id
      name: $name
      age: $age
      gender: $gender
      blood_group: $blood_group
      address: $address
      phone_number: $phone_number
    ) {
      message
    }
  }
`)
