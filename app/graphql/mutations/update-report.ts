import { graphql } from '~/gql'

export const UPDATE_REPORT = graphql(`
  mutation UpdateReport(
    $id: Int!
    $remarks: String
    $staff: String
    $sections_input: [SectionInput!]
    $fields_input: [FieldInput!]
    $template_id: Int
    $patient_id: Int
    $collection_date: DateTime
    $test_date: DateTime
    $report_generation_date: DateTime
    $doctor_id: Int
  ) {
    updateReport(
      id: $id
      remarks: $remarks
      staff: $staff
      sections_input: $sections_input
      fields_input: $fields_input
      template_id: $template_id
      patient_id: $patient_id
      collection_date: $collection_date
      test_date: $test_date
      report_generation_date: $report_generation_date
      doctor_id: $doctor_id
    ) {
      message
    }
  }`)
