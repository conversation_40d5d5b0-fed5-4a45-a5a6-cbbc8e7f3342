import { graphql } from '~/gql'

export const UPDATE_ESTABLISHMENT = graphql(`
  mutation UpdateEstablishment(
    $id: Int!
    $establishment_name: String
    $establishment_phone_number: String
    $establishment_address: String
    $wa_phone_number_id: String
    $logo: Upload
  ) {
    updateEstablishment(
      id: $id
      establishment_name: $establishment_name
      establishment_phone_number: $establishment_phone_number
      establishment_address: $establishment_address
      wa_phone_number_id: $wa_phone_number_id
      logo: $logo
    ) {
      message
    }
  }
`)
