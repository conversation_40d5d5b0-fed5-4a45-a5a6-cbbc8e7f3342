import { graphql } from '~/gql'

export const CREATE_TEMPLATE = graphql(`
  mutation CreateTemplate(
    $name: String!
    $title_color: String
    $report_name_color: String
    $sections_input: [SectionInput!]
    $fields_input: [FieldInput!]
  ) {
    createTemplate(
      name: $name
      title_color: $title_color
      report_name_color: $report_name_color
      sections_input: $sections_input
      fields_input: $fields_input
    ) {
      message
    }
  }
`)
