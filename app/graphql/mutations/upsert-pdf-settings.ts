import { graphql } from '~/gql'

export const UPSERT_PDF_SETTINGS = graphql(`
  mutation UpsertPdfSetting(
    $establishment_id: Int
    $title: String
    $subtitle: String
    $footer_left: String
    $footer_right: String
    $footer_center: String
    $logo: Upload
    $left_signature: Upload
    $right_signature: Upload
  ) {
    upsertPdfSetting(
      establishment_id: $establishment_id
      title: $title
      subtitle: $subtitle
      footer_left: $footer_left
      footer_right: $footer_right
      footer_center: $footer_center
      logo: $logo
      left_signature: $left_signature
      right_signature: $right_signature
    ) {
      message
    }
  }
`)
