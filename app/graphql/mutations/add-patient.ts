import { graphql } from '~/gql'

export const ADD_PATIENT = graphql(`
  mutation AddPatient(
    $name: String!
    $age: Int!
    $gender: String!
    $blood_group: String
    $address: String,
    $phone_number: String!
  ) {
    addPatient(
      name: $name
      age: $age
      gender: $gender
      blood_group: $blood_group
      address: $address
      phone_number: $phone_number
    ) {
      id
      name
      phone_number
      age
      gender
      blood_group
      address
    }
  }
`)
