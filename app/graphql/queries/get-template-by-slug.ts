import { graphql } from '~/gql'

export const GET_TEMPLATE_BY_SLUG = graphql(`
  query GetTemplateBySlug($slug: String!) {
    getTemplateBySlug(slug: $slug) {
      id
      name
      title_color
      report_name_color
      template_data {
        fields {
          name
          unit
          reference
          input_type
        }
        sections {
          name
          fields {
            name
            unit
            unit_active
            reference
            reference_active
            input_type
            input_type_values
            sub_fields {
              name
              input_type
              unit
              unit_value
              unit_active
              reference
              reference_value
              reference_active
            }
          }
        }
      }
    }
  }
`)
