import { graphql } from '~/gql'

export const GET_REPORT_BY_ID = graphql(`
  query GetReportById($id: Int!) {
    getReportById(id: $id) {
      id
      remarks
      staff
      collection_date
      test_date
      report_generation_date
      patient {
        id
        name
        phone_number
        age
        gender
        address
        blood_group
      }
      template_data {
        fields {
          name
          name_value
          unit
          unit_value
          reference
          reference_value
          input_type
          input_type_values
          sub_fields {
            name
            name_value
            input_type
            input_type_values
          }
        }
        sections {
          name
          name_value
          fields {
            name
            name_value
            unit
            unit_value
            unit_active
            reference
            reference_active
            reference_value
            input_type
            input_type_values
            sub_fields {
              name
              name_value
              input_type
              input_type_values
              unit
              unit_value
              unit_active
              reference
              reference_value
              reference_active
            }
          }
        }
      }
      template {
        id
        name
        title_color
        report_name_color
        template_data {
          fields {
            name
            unit
            reference
            input_type
          }
          sections {
            name
            name_value
            fields {
              name
              name_value
              unit
              unit_value
              unit_active
              reference
              reference_active
              reference_value
              input_type
              input_type_values
              sub_fields {
                name
                name_value
                input_type
                input_type_values
                unit
                unit_value
                unit_active
                reference
                reference_value
                reference_active
              }
            }
          }
        }
      }
      doctor {
        id
        name
      }
    }
  }
`)
