import { graphql } from '~/gql'

export const GET_ESTABLISHMENT_LIST = graphql(`
  query GetEstablishmentList($first: Int!, $page: Int, $name: String) {
    getEstablishmentList(
      first: $first
      page: $page
      name: $name
    ) {
      data {
        id
        name
        address
        phone_number
        owner {
          id
          username
        }
      }
      paginator_info {
        total
      }
    }
  }
`)
