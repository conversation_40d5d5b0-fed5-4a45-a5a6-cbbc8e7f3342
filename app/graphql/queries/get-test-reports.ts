import { graphql } from '~/gql'

export const GET_TEST_REPORTS = graphql(`
  query GetTestReports(
    $first: Int!
    $page: Int
    $start_date: DateTime
    $end_date: DateTime
    $report_name: String
    $patient_info: String
  ) {
    getTestReports(
      first: $first
      page: $page
      start_date: $start_date
      end_date: $end_date
      report_name: $report_name
      patient_info: $patient_info
    ) {
      data {
        id
        patient {
          id
          name
          phone_number
          address
          age
          gender
          blood_group
        }
        template {
          id
          name
          slug
          title_color
          report_name_color
        }
        template_data {
          fields {
            name
            name_value
            unit
            unit_value
            reference
            reference_value
            input_type
            input_type_values
            sub_fields {
              name
              name_value
              input_type
              input_type_values
            }
          }
          sections {
            name
            name_value
            fields {
              name
              name_value
              unit
              unit_value
              unit_active
              reference
              reference_value
              reference_active
              input_type
              input_type_values
              sub_fields {
                name
                name_value
                input_type
                input_type_values
                unit
                unit_value
                unit_active
                reference
                reference_value
                reference_active
              }
            }
          }
        }
        doctor {
          id
          name
        }
        latestWhatsAppReceipt {
          message_status
          error_message
        }
        created_at
        remarks
        staff
        collection_date
        report_generation_date
        test_date
      }
      paginator_info {
        total
      }
    }
  }                                      
`)
