import { useQuery } from '@tanstack/react-query'
import { GET_TEMPLATE_BY_SLUG } from '~/graphql/queries/get-template-by-slug'
import { graphqlClient } from '~/lib/graphql-client'

interface Props {
  slug?: string
}

export default function useGetTemplateBySlug({ slug }: Props) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-template-by-slug', slug],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_TEMPLATE_BY_SLUG,
        variables: {
          slug: slug ?? '',
        },
      })
    },
    enabled: !!slug,
  })

  return { data, isLoading, isError }
}
