import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { GET_ESTABLISHMENT_DOCTOR_LIST } from '~/graphql/queries/get-establishment-doctor-list'
import { graphqlClient } from '~/lib/graphql-client'

const first = 15

export default function useGetEstablishmentDoctorList() {
  const [search, setSearch] = useQueryState('doctor_search', parseAsString.withDefault(''))
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePageChange = (page: number) => {
    setPage(page)
  }

  const handleSearch = (search: string) => {
    setSearch(search)
    setPage(1)
  }

  const [keyword] = useDebounce(search, 500)

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-establishment-doctor-list', page, keyword],
    queryFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: GET_ESTABLISHMENT_DOCTOR_LIST,
        variables: {
          first,
          page,
          name: keyword,
        },
      })
    },
  })

  const totalItems = data?.getEstablishmentDoctorList?.paginator_info?.total || 0
  const totalPages = Math.ceil(totalItems / first) || 1
  return { data, isLoading, isError, handlePageChange, totalPages, page, handleSearch, search }
}
