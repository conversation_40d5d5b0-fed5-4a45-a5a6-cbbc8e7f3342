import { createFormHook, createFormHookContexts } from '@tanstack/react-form'

const { fieldContext, formContext } = createFormHookContexts()

// function TextField({ label }: { label: string }) {
//   const field = useFieldContext<string>()
//   return (
//     <label>
//       <div>{label}</div>
//       <input
//         value={field.state.value}
//         onChange={(e) => field.handleChange(e.target.value)}
//       />
//     </label>
//   )
// }

// function SubscribeButton({ label }: { label: string }) {
//   const form = useFormContext()
//   return (
//     <form.Subscribe selector={(state) => state.isSubmitting}>
//       {(isSubmitting) => <button disabled={isSubmitting}>{label}</button>}
//     </form.Subscribe>
//   )
// }

export const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    // TextField,
  },
  formComponents: {
    // SubscribeButton,
  },
  fieldContext,
  formContext,
})
