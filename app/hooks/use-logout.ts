import { useMutation } from '@tanstack/react-query'
import { LOGOUT } from '~/graphql/mutations/logout'
import { graphqlClient } from '~/lib/graphql-client'

function useLogout() {
  const logout = useMutation({
    mutationFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: LOGOUT,
      })
    },
  })

  return { logout }
}

export default useLogout
