import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { GET_ESTABLISHMENT_LIST } from '~/graphql/queries/get-establishment-list'
import { graphqlClient } from '~/lib/graphql-client'

const first = 15

export default function useGetEstablishmentList() {
  const [search, setSearch] = useQueryState('search', parseAsString.withDefault(''))
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePageChange = (page: number) => {
    setPage(page)
  }

  const handleSearch = (search: string) => {
    setSearch(search)
    setPage(1)
  }

  const [keyword] = useDebounce(search, 500)

  const { data, isLoading, isError } = useQuery({
    queryKey: ['establishment-list', page, keyword, first],
    queryFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: GET_ESTABLISHMENT_LIST,
        variables: {
          first,
          page,
          name: keyword,
        },
      })
    },
  })

  const totalItems = data?.getEstablishmentList?.paginator_info?.total || 0
  const totalPages = Math.ceil(totalItems / first) || 1

  return {
    data,
    isLoading,
    isError,
    totalPages,
    handlePageChange,
    page,
    handleSearch,
    search,
  }
}
