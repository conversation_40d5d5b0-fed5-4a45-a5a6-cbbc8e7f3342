import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { GET_PATIENT_LIST } from '~/graphql/queries/get-patients'
import { graphqlClient } from '~/lib/graphql-client'

interface Props {
  first?: number
}

export default function useGetPatientList({ first = 15 }: Props) {
  const [search, setSearch] = useQueryState('search', parseAsString.withDefault(''))
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePageChange = (page: number) => {
    setPage(page)
  }

  const handleSearch = (search: string) => {
    setSearch(search)
    setPage(1)
  }

  const [keyword] = useDebounce(search, 500)

  const { data, isLoading, isError } = useQuery({
    queryKey: ['patient-list', page, keyword, first],
    queryFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: GET_PATIENT_LIST,
        variables: {
          first,
          page,
          keyword,
        },
      })
    },
  })

  const totalItems = data?.getPatientList?.paginator_info?.total || 0
  const totalPages = Math.ceil(totalItems / first) || 1

  return { data, isLoading, isError, setSearch, handlePageChange, totalPages, page, handleSearch, search }
}
