import { useQuery } from '@tanstack/react-query'
import { GET_PDF_SETTINGS } from '~/graphql/queries/get-pdf-settings'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetPdfSettings() {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-pdf-settings'],
    queryFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: GET_PDF_SETTINGS,
      })
    },
  })

  return { data, isLoading, isError }
}
