import { useQuery } from '@tanstack/react-query'
import { GET_REPORT_BY_ID } from '~/graphql/queries/get-report-by-id'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetReportById({ id }: { id: number }) {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-report-by-id', id],
    queryFn: async () => {
      const client = await graphqlClient()
      return await client.request({
        document: GET_REPORT_BY_ID,
        variables: { id },
      })
    },
  })

  return { data, isLoading, isError }
}
