import { useQuery } from '@tanstack/react-query'
import { GET_ME } from '~/graphql/queries/get-me'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetMe() {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-me'],
    queryFn: async () => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: GET_ME,
      })
    },
  })

  return { data, isLoading, isError }
}
