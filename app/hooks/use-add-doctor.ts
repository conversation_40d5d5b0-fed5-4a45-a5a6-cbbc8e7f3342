import type { AddDoctorType } from '~/lib/types/doctor-schema'
import { useMutation } from '@tanstack/react-query'
import { ADD_DOCTOR } from '~/graphql/mutations/add-doctor'
import { graphqlClient } from '~/lib/graphql-client'

export default function useAddDoctor() {
  const addDoctor = useMutation({
    mutationFn: async (data: AddDoctorType) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: ADD_DOCTOR,
        variables: {
          ...data,
        },
      })
    },
  })

  return { addDoctor }
}
