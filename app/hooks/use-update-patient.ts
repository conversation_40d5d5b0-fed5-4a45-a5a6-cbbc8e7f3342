import type { UpdatePatientType } from '~/lib/types/update-patient-schema'
import { useMutation } from '@tanstack/react-query'
import { UPDATE_PATIENT } from '~/graphql/mutations/update-patient'
import { graphqlClient } from '~/lib/graphql-client'

export default function useUpdatePatient() {
  const updatePatient = useMutation({
    mutationFn: async ({id, data}: {id: number, data: UpdatePatientType}) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_PATIENT,
        variables: {
          id,
          ...data,
        },
      })
    },
  })

  return {
    updatePatient,
  }
}
