import { useMutation } from '@tanstack/react-query'
import { DELETE_PATIENT } from '~/graphql/mutations/delete-patient'
import { graphqlClient } from '~/lib/graphql-client'

export default function useDeletePatient() {
  const deletePatient = useMutation({
    mutationFn: async (id: number) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: DELETE_PATIENT,
        variables: {
          id,
        },
      })
    },
  })

  return { deletePatient }
}
