import type { AddPatientType } from '~/lib/types/add-patient-schema'
import { useMutation } from '@tanstack/react-query'
import { ADD_PATIENT } from '~/graphql/mutations/add-patient'
import { graphqlClient } from '~/lib/graphql-client'

export default function useAddPatient() {
  const addPatient = useMutation({
    mutationFn: async (data: AddPatientType) => {
      const client = await graphqlClient()
      return client.request({
        document: ADD_PATIENT,
        variables: {
          ...data,
        },
      })
    },
  })

  return {
    addPatient,
  }
}
