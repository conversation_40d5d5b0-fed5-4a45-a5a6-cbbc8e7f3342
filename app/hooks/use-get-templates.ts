import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { GET_TEMPLATES } from '~/graphql/queries/get-templates'
import { graphqlClient } from '~/lib/graphql-client'

const FIRST = 20

export default function useGetTemplates() {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePageChange = (page: number) => {
    setPage(page)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-templates', page],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_TEMPLATES,
        variables: {
          first: FIRST,
          page,
        },
      })
    },
  })

  const totalItems = data?.getTemplates?.paginator_info?.total || 0
  const totalPages = Math.ceil(totalItems / FIRST) || 1

  return { data, isLoading, isError, handlePageChange, page, totalPages }
}
