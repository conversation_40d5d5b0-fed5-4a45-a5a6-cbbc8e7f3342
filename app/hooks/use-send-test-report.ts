import { useMutation } from '@tanstack/react-query'
import { SEND_TEST_REPORT } from '~/graphql/mutations/send-test-report'
import { graphqlClient } from '~/lib/graphql-client'

export default function useSendTestReport() {
  const sendTestReport = useMutation({
    mutationFn: async ({ report_id, patient_id, pdf, whatsapp_number }: { report_id: number, patient_id: number, pdf: Blob, whatsapp_number: string }) => {
      const graphql = await graphqlClient()
      return graphql.request({
        document: SEND_TEST_REPORT,
        variables: {
          report_id,
          patient_id,
          whatsapp_number,
          pdf, // Assuming the PDF is already available in the report object
        },
      })
    },
  })

  return { sendTestReport }
}
