{"name": "medlab", "type": "module", "private": true, "scripts": {"build:production": "react-router build", "build:staging": "NODE_ENV=test react-router build", "dev": "react-router dev", "start:production": "PORT=3000 react-router-serve ./build/server/index.js", "start:staging": "NODE_ENV=test PORT=3001 react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "codegen": "graphql-codegen --config codegen.ts", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "dependencies": {"@graphql-typed-document-node/core": "^3.2.0", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-tooltip": "^1.2.4", "@react-pdf/renderer": "^4.3.0", "@react-router/fs-routes": "^7.5.3", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "@tanstack/react-form": "^1.9.0", "@tanstack/react-query": "^5.74.11", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "graphql": "^16.11.0", "graphql-request": "^7.1.2", "isbot": "^5.1.27", "lucide-react": "^0.503.0", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "react-pdf-html": "^2.1.3", "react-router": "^7.5.3", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.8", "use-debounce": "^10.0.4", "zod": "^3.24.3"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint-react/eslint-plugin": "^1.43.0", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/client-preset": "^4.8.0", "@graphql-codegen/introspection": "^4.0.3", "@react-router/dev": "^7.5.3", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.5", "@types/node": "^22", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "eslint": "^9.24.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "husky": "^9.1.7", "lint-staged": "^16.1.0", "react-router-devtools": "^5.0.0", "tailwindcss": "^4.1.5", "typescript": "^5.8.3", "vite": "^6.3.4", "vite-tsconfig-paths": "^5.1.4"}, "overrides": {"react-is": "^19.1.0"}, "volta": {"node": "22.11.0"}}