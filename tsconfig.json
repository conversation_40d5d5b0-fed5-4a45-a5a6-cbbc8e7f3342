{"compilerOptions": {"target": "ES2022", "jsx": "react-jsx", "lib": ["DOM", "DOM.Iterable", "ES2022"], "baseUrl": ".", "rootDirs": [".", "./.react-router/types"], "module": "ES2022", "moduleResolution": "bundler", "paths": {"~/*": ["./app/*"]}, "resolveJsonModule": true, "types": ["node", "vite/client"], "strict": true, "noEmit": true, "esModuleInterop": true, "verbatimModuleSyntax": true, "skipLibCheck": true}, "include": ["**/*", "**/.server/**/*", "**/.client/**/*", ".react-router/types/**/*"]}